import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Response } from '@app/core/interfaces/responses/response.interface';
import { Rol } from '@app/core/interfaces/rol.interface';
import { environment } from '@src/environments/environment';
import { Observable } from 'rxjs';

@Injectable()
export class RolService {
  private apiUrl = environment.urlMsUserRoles;

  constructor(private http: HttpClient) {}

  getRolList(): Observable<Response<Rol[]>> {
    return this.http.get<Response<Rol[]>>(`${this.apiUrl}.json`);
  }
}
