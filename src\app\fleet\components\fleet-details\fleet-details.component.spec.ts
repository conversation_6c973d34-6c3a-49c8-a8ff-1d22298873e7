import { ComponentFixture, TestBed } from '@angular/core/testing';

import { FleetDetailsComponent } from './fleet-details.component';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { MatDialogModule } from '@angular/material/dialog';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { FleetService } from '@app/services/fleet/fleet.service';

describe('FleetDetailsComponent', () => {
  let component: FleetDetailsComponent;
  let fixture: ComponentFixture<FleetDetailsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [FleetDetailsComponent],
      providers: [FleetService],
      imports: [
        HttpClientTestingModule,
        NoopAnimationsModule,
        MatDialogModule,
        MatSnackBarModule,
      ],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(FleetDetailsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
