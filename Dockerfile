FROM node:14-alpine as builder

WORKDIR /usr/src/app

COPY package.json package-lock.json ./

RUN npm i --legacy-peer-deps

COPY . .

RUN NG_APP_API_BASE_DOMAIN="\$API_BASE_DOMAIN" NG_APP_AUTH_CLIENT_ID="\$AUTH_CLIENT_ID" npm run build

# hadolint ignore=DL3007
FROM flashspys/nginx-static:latest
COPY nginx.conf /etc/nginx/conf.d/default.conf
COPY --from=builder /usr/src/app/dist/delivery-glider /static
COPY entrypoint.sh /entrypoint.sh

ENV API_BASE_DOMAIN "uphi.cc"
ENV AUTH_CLIENT_ID ''

ENTRYPOINT ["/entrypoint.sh"]
CMD ["nginx", "-g", "daemon off;"]
