import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { GliderStatusLog } from '@app/core/interfaces/glider-status-log.interface';
import { environment } from '@src/environments/environment';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class GliderStatusLogsService {
  private apiUrl = environment.urlMsFleet;

  constructor(private http: HttpClient) {}

  getGliderStatusLogs(gliderName: string): Observable<GliderStatusLog[]> {
    return this.http.get<GliderStatusLog[]>(`${this.apiUrl}/logs/glider-statuses/glider-name/${gliderName}`);
  }
}
