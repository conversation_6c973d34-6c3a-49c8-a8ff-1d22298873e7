export interface User {
  id: number;
  email: string;
  user_role_id: number;
  first_name: string;
  last_name: string;
  phone_country?: string | null;
  phone_number?: string | null;
  country?: string | null;
  address_line1?: string | null;
  address_line2?: string | null;
  city?: string | null;
  zip_code?: string | null;
  vat?: string | null;
  picture_url?: string | null;
  balance?: string | null;
  display_currency?: string | null;
  referral_code?: string | null;
  referral_code_used_id?: string | null;
  employed_by_id?: string | null;
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
  created_at: string;
  updated_at: string;
}
