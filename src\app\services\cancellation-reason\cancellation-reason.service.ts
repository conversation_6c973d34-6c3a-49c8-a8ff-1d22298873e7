import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { CancellationReason, CancellationReasonDto, CATEGORY_MAPPING } from '@app/core/interfaces/cancellation-reason.interface';
import { environment } from '@src/environments/environment';
import { KeycloakService } from 'keycloak-angular';
import { Observable, from, of, throwError } from 'rxjs';
import { catchError, map, switchMap, tap } from 'rxjs/operators';

@Injectable()
export class CancellationReasonService {
  private apiUrl = environment.urlMsRides;
  private cachedReasons: CancellationReason[] | null = null;

  constructor(private http: HttpClient, private keycloakService: KeycloakService) { }


  getCancellationReasons(): Observable<CancellationReason[]> {
    this.cachedReasons = null;

    return from(this.keycloakService.getToken()).pipe(
      switchMap((token) => {
        const headers = new HttpHeaders({
          Authorization: `Bearer ${token}`,
        });

        return this.http.get<CancellationReasonDto[]>(`${this.apiUrl}/rides/cancel-reasons`, { headers }).pipe(
          map(reasons => this.mapToInternalFormat(reasons)),
          catchError(() => {
            return of([]);
          })
        );
      })
    );
  }


  private mapToInternalFormat(reasons: CancellationReasonDto[]): CancellationReason[] {
    const mappedReasons = reasons.map(reason => {
      const category = this.determineCategoryFromName(reason.name);
      return {
        id: reason.id.toString(),
        name: reason.name,
        category: category.name as 'Environmental' | 'Customer' | 'Technical' | 'Operations',
        color: category.color
      };
    });

    this.cachedReasons = mappedReasons;
    return mappedReasons;
  }


  private determineCategoryFromName(name: string): { name: string, color: string } {
    const lowerName = name.toLowerCase();

    for (const [category, data] of Object.entries(CATEGORY_MAPPING)) {
      if (data.keywords.some(keyword => lowerName.includes(keyword.toLowerCase()))) {
        return { name: category, color: data.color };
      }
    }

    return { name: 'Technical', color: CATEGORY_MAPPING['Technical'].color };
  }


  createCancellationReason(reason: { name: string, description: string }): Observable<CancellationReasonDto> {
    return from(this.keycloakService.getToken()).pipe(
      switchMap((token) => {
        const headers = new HttpHeaders({
          Authorization: `Bearer ${token}`,
        });

        return this.http.post<CancellationReasonDto>(`${this.apiUrl}/rides/cancel-reasons`, reason, { headers }).pipe(
          tap(() => {
            this.cachedReasons = null;
          }),
          catchError((error) => {
            return throwError(error);
          })
        );
      })
    );
  }

  getCancellationReasonDetails(reason: any): Observable<CancellationReason | null> {
    return this.getCancellationReasons().pipe(
      map(reasons => {
        if (!reason) return null;

        if (typeof reason === 'object' && reason !== null) {
          if (reason.id && reason.name) {
            let cancellationReason = reasons.find(r => r.id === String(reason.id));
            if (cancellationReason) return cancellationReason;

            const reasonName = String(reason.name).toLowerCase();
            cancellationReason = reasons.find(r => r.name.toLowerCase() === reasonName);
            if (cancellationReason) return cancellationReason;

            if (reasonName.includes('environmental')) {
              cancellationReason = reasons.find(r => r.category === 'Environmental');
            } else if (reasonName.includes('customer') || reasonName.includes('client')) {
              cancellationReason = reasons.find(r => r.category === 'Customer');
            } else if (reasonName.includes('technical') || reasonName.includes('issue')) {
              cancellationReason = reasons.find(r => r.category === 'Technical');
            } else if (reasonName.includes('operations') || reasonName.includes('team')) {
              cancellationReason = reasons.find(r => r.category === 'Operations');
            }

            return cancellationReason || null;
          }
          return null;
        }

        if (typeof reason === 'string') {
          const reasonStr = reason.toLowerCase();

          let cancellationReason = reasons.find(r =>
            r.name.toLowerCase() === reasonStr ||
            r.id.toLowerCase() === reasonStr
          );

          if (!cancellationReason) {
            if (reasonStr.includes('environmental')) {
              cancellationReason = reasons.find(r => r.category === 'Environmental');
            } else if (reasonStr.includes('customer') || reasonStr.includes('client')) {
              cancellationReason = reasons.find(r => r.category === 'Customer');
            } else if (reasonStr.includes('technical') || reasonStr.includes('issue')) {
              cancellationReason = reasons.find(r => r.category === 'Technical');
            } else if (reasonStr.includes('operations') || reasonStr.includes('team')) {
              cancellationReason = reasons.find(r => r.category === 'Operations');
            }
          }

          return cancellationReason || null;
        }

        return null;
      })
    );
  }
}
