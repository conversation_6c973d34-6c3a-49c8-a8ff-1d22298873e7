<div class="timing-history-container">
  <div class="timing-history-header">
    <h2>Time Tracking History</h2>
    <div class="timing-history-summary">
      {{ rideTimings.length }} recording{{ rideTimings.length === 1 ? '' : 's' }}, Total: {{ getFormattedTotalTime() }}
    </div>
    <button mat-icon-button class="close-button" (click)="onClose()">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <div class="timing-history-content">
    <div *ngIf="rideTimings.length === 0" class="no-timings">
      <p>No time tracking records yet.</p>
    </div>

    <div class="table-container">
      <table *ngIf="rideTimings.length > 0" class="timing-table">
        <thead>
          <tr>
            <th style="width: 160px;">Duration</th>
            <th style="width: 180px;">Operator</th>
            <th style="width: 300px;">Time Period</th>
            <th>Description</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let timing of rideTimings; let i = index" class="timing-row" [class.server-row]="timing.fromServer">
            <!-- Duration Cell -->
            <td class="duration-cell">
              <ng-container *ngIf="editingIndex !== i || editingField !== 'duration'">
                <div class="editable-cell" (click)="startEditing(i, 'duration')" matTooltip="Click to edit duration">
                  <span class="cell-value">{{ formatMilliseconds(timing.ms) }}</span>
                  <mat-icon class="edit-icon">edit</mat-icon>
                </div>
              </ng-container>
              <ng-container *ngIf="editingIndex === i && editingField === 'duration'">
                <div class="edit-container">
                  <mat-form-field appearance="outline" class="edit-field">
                    <input matInput [formControl]="editTimeControl" placeholder="00:00:00,0"
                           (keydown.enter)="saveEditing()" (keydown.escape)="cancelEditing()">
                  </mat-form-field>
                  <div class="edit-actions">
                    <button mat-icon-button color="primary" (click)="saveEditing()" matTooltip="Save">
                      <mat-icon>check</mat-icon>
                    </button>
                    <button mat-icon-button (click)="cancelEditing()" matTooltip="Cancel">
                      <mat-icon>close</mat-icon>
                    </button>
                  </div>
                </div>
              </ng-container>
            </td>

            <td>{{ timing.name }}</td>

            <td>
              <ng-container *ngIf="editingIndex !== i || editingField !== 'timeperiod'">
                <div class="editable-cell" (click)="startEditing(i, 'timeperiod')" matTooltip="Click to edit time period">
                  <span class="cell-value">{{ formatTimestamp(timing.startTimestamp) }} - {{ formatTimestampToTime(timing.endTimestamp) }}</span>
                  <mat-icon class="edit-icon">edit</mat-icon>
                </div>
              </ng-container>
              <ng-container *ngIf="editingIndex === i && editingField === 'timeperiod'">
                <div class="edit-container timeperiod-edit">
                  <div class="timeperiod-fields">
                    <mat-form-field appearance="outline" class="edit-field">
                      <mat-label>Start</mat-label>
                      <input matInput [formControl]="editStartTimeControl" type="datetime-local">
                    </mat-form-field>
                    <mat-form-field appearance="outline" class="edit-field">
                      <mat-label>End</mat-label>
                      <input matInput [formControl]="editEndTimeControl" type="datetime-local">
                    </mat-form-field>
                  </div>
                  <div class="edit-actions">
                    <button mat-icon-button color="primary" (click)="saveTimeperiodEditing()" matTooltip="Save">
                      <mat-icon>check</mat-icon>
                    </button>
                    <button mat-icon-button (click)="cancelEditing()" matTooltip="Cancel">
                      <mat-icon>close</mat-icon>
                    </button>
                  </div>
                </div>
              </ng-container>
            </td>

            <td>
              <ng-container *ngIf="editingIndex !== i || editingField !== 'description'">
                <div class="editable-cell" (click)="startEditing(i, 'description')" matTooltip="Click to edit description">
                  <span class="cell-value">{{ timing.description || 'No description' }}</span>
                  <mat-icon class="edit-icon">edit</mat-icon>
                </div>
              </ng-container>
              <ng-container *ngIf="editingIndex === i && editingField === 'description'">
                <div class="edit-container">
                  <mat-form-field appearance="outline" class="edit-field description-field">
                    <input matInput [formControl]="editDescriptionControl" placeholder="Enter description"
                           (keydown.enter)="saveDescriptionEditing()" (keydown.escape)="cancelEditing()">
                  </mat-form-field>
                  <div class="edit-actions">
                    <button mat-icon-button color="primary" (click)="saveDescriptionEditing()" matTooltip="Save">
                      <mat-icon>check</mat-icon>
                    </button>
                    <button mat-icon-button (click)="cancelEditing()" matTooltip="Cancel">
                      <mat-icon>close</mat-icon>
                    </button>
                  </div>
                </div>
              </ng-container>

              <div class="status-badges" *ngIf="editingIndex !== i">
                <span *ngIf="timing.edited" class="edited-badge">edited</span>
                <span *ngIf="timing.fromServer" class="server-badge">server</span>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
