import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DialogLocationEditNameComponent } from './dialog-location-edit-name.component';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { LocationService } from '@app/services/location/location.service';
import { AuthModule } from '@auth/auth.module';

describe('DialogLocationEditNameComponent', () => {
  let component: DialogLocationEditNameComponent;
  let fixture: ComponentFixture<DialogLocationEditNameComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DialogLocationEditNameComponent],
      providers: [
        {
          provide: MAT_DIALOG_DATA,
          useValue: {},
        },
        {
          provide: MatDialogRef,
          useValue: {},
        },
        LocationService,
      ],
      imports: [AuthModule],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DialogLocationEditNameComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
