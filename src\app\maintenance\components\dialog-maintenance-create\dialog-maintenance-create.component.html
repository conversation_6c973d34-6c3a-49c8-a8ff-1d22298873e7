<mat-dialog-content>
  <form [formGroup]="form" (submit)="save()">
    <div class="row mt-3">
      <div class="col-4" *ngIf="edit">
        <label>
          Maintenance ID
          <input formControlName="id" class="form-control mt-2 input-style-form" type="text" [attr.disabled]="true">
        </label>
      </div>
    </div>

    <div class="row mt-3">
      <div class="col-6">
        <label>
          Due Date
          <input formControlName="due_date" class="form-control mt-2" [valueAsDate]="dueDate" (change)="updateDueDate($event)" name="due_date" type="date" id="date"/>
          <mat-error *ngIf="formDialog.due_date?.errors?.required && formDialog.due_date.touched">Due Date is
            required</mat-error>
        </label>
      </div>
      <div class="col-6">
        <label>
          Completed Date
          <input formControlName="completed_date" class="form-control mt-2" [valueAsDate]="completedDate" (change)="updateCompletedDate($event)" name="completed_date" type="date" id="date"/>
        </label>
      </div>
    </div>

    <div class="row mt-3">
      <label class="w-100">
        Glider
          <br>
          <mat-form-field class="w-100 mt-2 mat-autocomplete" appearance="outline" floatLabel="never">
              <mat-icon matPrefix>search</mat-icon>
              <input type="text" placeholder="Search by name or ID" matInput formControlName="glider" [matAutocomplete]="auto" required>
              <mat-autocomplete autoActiveFirstOption #auto="matAutocomplete" [displayWith]="displayFn">
                  <mat-option *ngFor="let item of filteredOptions | async" [value]="item">
                      {{item.name}}
                  </mat-option>
              </mat-autocomplete>
              <mat-error *ngIf="formDialog.glider?.errors?.required && formDialog.glider?.touched">Glider is
                required</mat-error>
          </mat-form-field>
      </label>
    </div>

    <div class="row mt-3">
      <label class="w-100">
        Maintenance Type
          <br>
          <mat-select placeholder="Maintenance Type" class="form-control input-style me-3 w-100" appearance="standard" floatLabel="never" formControlName="maintenance_type">
            <mat-option *ngFor="let item of maintenanceTypes" [value]="item.id">
                {{item.name}}
            </mat-option>
          </mat-select>
      </label>
    </div>
    <div class="row mt-3">
      <label class="w-100">
        Maintenance Staff
        <br>
        <mat-select
          formControlName="maintenance_staff"
          placeholder="Select staff member"
          class="form-control input-style me-3 w-100"
          appearance="standard"
          floatLabel="never"
        >
          <mat-option *ngFor="let user of keycloakUsers" [value]="user.email">
            {{ user.username }}
          </mat-option>
        </mat-select>
        <mat-error *ngIf="formDialog.maintenance_staff?.errors?.required && formDialog.maintenance_staff?.touched">
          Maintenance Staff is required
        </mat-error>
      </label>
    </div>

    <div class="row mt-3">
      <div class="col-12">
        <mat-checkbox class="custom-checkbox" formControlName="postMaintenanceChecklistDone">Post Maintenance Checklist Done</mat-checkbox>
      </div>
    </div>

    <div class="row mt-3">
      <label>
        Notes
        <br>
        <textarea rows="5" formControlName="notes" class="form-control mt-2"></textarea>
      </label>
    </div>

    <mat-dialog-actions align="end" class="mt-3">
      <button mat-button mat-dialog-close class="btn-cancel">Cancel</button>
      <button mat-button cdkFocusInitial class="btn-green" type="submit">Save</button>
    </mat-dialog-actions>
    <br>
  </form>
</mat-dialog-content>
