import {Injectable} from '@angular/core';
import {HttpClient, HttpHeaders} from '@angular/common/http';
import {Observable, from} from 'rxjs';
import {switchMap, map} from 'rxjs/operators';
import {KeycloakService} from 'keycloak-angular';

import {environment} from '@src/environments/environment';
import {
  SoftwareVersion,
  CreateSoftwareVersionDto,
  CreateSoftwareVersionTypeDto,
  SoftwareVersionType
} from '@core/interfaces/softwareversion.interface';

@Injectable({
  providedIn: 'root'
})
export class SoftwareVersionService {
  private baseUrl = environment.urlMsFleet;

  private versionsUrl = `${this.baseUrl}/software-versions`;
  private versionTypesUrl = `${this.baseUrl}/software-version-types`;

  constructor(
    private http: HttpClient,
    private keycloakService: KeycloakService
  ) {
  }


  private getAuthHeaders(): Observable<HttpHeaders> {
    return from(this.keycloakService.getToken()).pipe(
      map(token => new HttpHeaders({Authorization: `Bearer ${token}`}))
    );
  }

  getSoftwareVersions(): Observable<SoftwareVersion[]> {
    return this.getAuthHeaders().pipe(
      switchMap(headers => {
        return this.http.get<SoftwareVersion[]>(this.versionsUrl, {headers});
      })
    );
  }


  createSoftwareVersion(payload: CreateSoftwareVersionDto): Observable<SoftwareVersion> {
    return this.getAuthHeaders().pipe(
      switchMap(headers => {
        return this.http.post<SoftwareVersion>(this.versionsUrl, payload, {headers});
      })
    );
  }


  getSoftwareVersionById(id: number): Observable<SoftwareVersion> {
    return this.getAuthHeaders().pipe(
      switchMap(headers => {
        return this.http.get<SoftwareVersion>(`${this.versionsUrl}/${id}`, {headers});
      })
    );
  }


  getSoftwareVersionsByTypeId(softwareVersionTypeId: number): Observable<SoftwareVersion[]> {
    return this.getAuthHeaders().pipe(
      switchMap(headers => {
        return this.http.get<SoftwareVersion[]>(`${this.baseUrl}/software-versions/software-version-type/${softwareVersionTypeId}`, {headers});
      })
    );
  }


  getSoftwareVersionTypes(): Observable<SoftwareVersionType[]> {
    return this.getAuthHeaders().pipe(
      switchMap(headers => {
        return this.http.get<SoftwareVersionType[]>(this.versionTypesUrl, {headers});
      })
    );
  }


  createSoftwareVersionType(payload: CreateSoftwareVersionTypeDto): Observable<SoftwareVersionType> {
    return this.getAuthHeaders().pipe(
      switchMap(headers => {
        return this.http.post<SoftwareVersionType>(this.versionTypesUrl, payload, {headers});
      })
    );
  }
}
