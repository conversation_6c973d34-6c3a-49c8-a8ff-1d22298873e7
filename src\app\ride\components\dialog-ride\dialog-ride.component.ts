import { Component, OnInit, Inject, OnD<PERSON>roy } from '@angular/core';
import { of, Subject } from 'rxjs';
import { FormControl, FormGroup } from '@angular/forms';

import {
  MAT_DIALOG_DATA,
  MatDialogRef,
  MatDialog,
} from '@angular/material/dialog';
import { NotificationsService } from '@shared/services/notifications.service';

import { Ride } from '@app/core/interfaces/ride.interface';
import { DatePipe } from '@angular/common';
import { DialogRideOperatorComponent } from '../dialog-ride-operator/dialog-ride-operator.component';
import { DialogRideGliderComponent } from '../dialog-ride-glider/dialog-ride-glider.component';
import { RideService } from '@app/services/ride/ride.service';
import { catchError, takeUntil } from 'rxjs/operators';
import { typeAction } from '@app/shared/utils/enum';

@Component({
  selector: 'app-dialog-ride',
  templateUrl: './dialog-ride.component.html',
  styleUrls: ['./dialog-ride.component.css'],
})
export class DialogRideComponent implements OnInit, OnDestroy {
  form!: FormGroup;
  subject = new Subject<any>();

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: Ride,
    private notificationsAlerts: NotificationsService,
    public dialogRef: MatDialogRef<DialogRideComponent>,
    private rideService: RideService,
    public datepipe: DatePipe,
    public dialog: MatDialog
  ) {
    this.onForm();
  }

  onForm(): void {
    this.form = new FormGroup({
      id: new FormControl(this.data.id, []),
      operator: new FormControl(
        this.data.operator_id,
        []
      ),
      location_departure: new FormControl(this.data.from_location, []),
      location_arrival: new FormControl(this.data.to_location, []),
      time_departure: new FormControl(
        this.datepipe.transform(this.data.departure_time, 'M/d/yy, h:mm a'),
        []
      ),
      time_arrival: new FormControl(
        this.datepipe.transform(this.data.arrival_time, 'M/d/yy, h:mm a'),
        []
      ),
      status: new FormControl(this.data.ride_status_id, []),
      glider_id: new FormControl(this.data.glider_name, []),
    });
  }

  ngOnInit(): void {
    this.rideService
      .listenDataSubjectRide()
      .pipe(
        takeUntil(this.subject),
        catchError((err) => {
          this.notificationsAlerts.openSnackBar('Error', 'Ok');
          return of(err);
        })
      )
      .subscribe((res) => {
        if (this.rideService.action === typeAction.updated) {
          this.data = res;
          this.onForm();
        }
      });
  }

  get formDialog(): any {
    return this.form.controls;
  }

  ngOnDestroy(): void {
    this.subject.complete();
    this.subject.unsubscribe();
  }

  openDialogOperator(): void {
    this.dialog.open(DialogRideOperatorComponent, {
      height: 'auto',
      width: '600px',
      data: this.data,
    });
  }

  openDialogGlider(): void {
    this.dialog.open(DialogRideGliderComponent, {
      height: 'auto',
      width: '600px',
      data: this.data,
    });
  }

  openDialogGcs(): void {
    // this.dialog.open(DialogRideGscComponent, {
    //   height: 'auto',
    //   width: '600px',
    //   data: this.data,
    // });
  }
}
