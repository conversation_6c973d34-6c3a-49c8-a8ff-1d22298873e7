import {Component, OnInit} from '@angular/core';
import {MatDialogRef} from '@angular/material/dialog';
import {FormBuilder, FormGroup, Validators} from '@angular/forms';
import {CancellationReason} from '@app/core/interfaces/cancellation-reason.interface';
import {CancellationReasonService} from '@app/services/cancellation-reason/cancellation-reason.service';

@Component({
  selector: 'app-cancel-reason-dialog',
  template: `
    <h2 mat-dialog-title>Cancel Reason</h2>
    <mat-dialog-content [formGroup]="form" class="p-3">
      <div *ngIf="loading" class="loading-container">
        <mat-spinner diameter="40"></mat-spinner>
      </div>

      <div *ngIf="!loading">
        <mat-form-field appearance="fill" class="w-100">
          <mat-label>Select Cancel Reason</mat-label>
          <mat-select formControlName="reason" required>
            <mat-option *ngFor="let option of reasonOptions" [value]="option.name">
              {{ option.name }}
            </mat-option>
          </mat-select>
          <mat-error *ngIf="form.get('reason')?.hasError('required')">
            Reason is required
          </mat-error>
        </mat-form-field>

        <mat-form-field *ngIf="isCustomerCancellation()" appearance="fill" class="w-100 mt-3">
          <mat-label>Additional information</mat-label>
          <textarea matInput formControlName="customer_notes" rows="3"></textarea>
          <mat-error *ngIf="form.get('customer_notes')?.hasError('required')">
            Additional information is required
          </mat-error>
        </mat-form-field>

        <mat-checkbox formControlName="delivered_by_car" class="mt-3 d-block">
          The operators will deliver the package by car.
        </mat-checkbox>
      </div>
    </mat-dialog-content>
    <mat-dialog-actions align="end" class="p-2">
      <button mat-button (click)="onCancel()">Cancel</button>
      <button mat-raised-button color="primary" [disabled]="form.invalid || loading" (click)="onSave()">Save</button>
    </mat-dialog-actions>
  `,
  styles: [`
    .w-100 {
      width: 100%;
    }

    .p-3 {
      padding: 1rem;
    }

    .p-2 {
      padding: 0.5rem;
    }

    .mt-3 {
      margin-top: 1rem;
    }

    .d-block {
      display: block;
    }

    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100px;
    }
  `]
})
export class CancelReasonDialogComponent implements OnInit {
  form: FormGroup;
  reasonOptions: CancellationReason[] = [];
  loading = true;

  constructor(
    public dialogRef: MatDialogRef<CancelReasonDialogComponent>,
    private fb: FormBuilder,
    private cancellationReasonService: CancellationReasonService
  ) {
    this.form = this.fb.group({
      reason: ['', Validators.required],
      delivered_by_car: [false],
      customer_notes: ['']
    });

    this.form.get('reason')?.valueChanges.subscribe(value => {
      if (value && typeof value === 'string' && value.toLowerCase().includes('customer')) {
        this.form.get('customer_notes')?.setValidators([Validators.required]);
      } else {
        this.form.get('customer_notes')?.clearValidators();
      }
      this.form.get('customer_notes')?.updateValueAndValidity();
    });
  }

  ngOnInit(): void {
    this.loadCancellationReasons();
  }

  private loadCancellationReasons(): void {
    this.loading = true;
    this.cancellationReasonService.getCancellationReasons().subscribe(
      (reasons) => {
        this.reasonOptions = reasons;
        this.loading = false;
      },
      () => {
        this.loading = false;
      }
    );
  }

  isCustomerCancellation(): boolean {
    const reason = this.form.get('reason')?.value;
    return reason && typeof reason === 'string' && reason.toLowerCase().includes('customer');
  }

  onSave(): void {
    let finalReason = this.form.value.reason;
    const selectedReason = this.reasonOptions.find(r => r.name === this.form.value.reason);
    const reasonId = selectedReason ? parseInt(selectedReason.id) : null;

    if (this.isCustomerCancellation() && this.form.value.customer_notes) {
      finalReason = `${finalReason}: ${this.form.value.customer_notes}`;
    }

    this.dialogRef.close({
      reason: finalReason,
      delivered_by_car: this.form.value.delivered_by_car,
      cancel_reason_id: reasonId
    });
  }

  onCancel(): void {
    this.dialogRef.close();
  }
}
