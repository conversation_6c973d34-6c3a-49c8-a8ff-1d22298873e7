import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DialogMaintenanceCreateComponent } from './dialog-maintenance-create.component';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { AuthModule } from '@auth/auth.module';
import { GliderMaintenanceService } from '@app/services/gliderMaintenance/gliderMaintenance.service';

describe('DialogMaintenanceCreateComponent', () => {
  let component: DialogMaintenanceCreateComponent;
  let fixture: ComponentFixture<DialogMaintenanceCreateComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DialogMaintenanceCreateComponent],
      providers: [
        {
          provide: MAT_DIALOG_DATA,
          useValue: {},
        },
        {
          provide: MatDialogRef,
          useValue: {},
        },
        GliderMaintenanceService,
      ],
      imports: [AuthModule],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DialogMaintenanceCreateComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
