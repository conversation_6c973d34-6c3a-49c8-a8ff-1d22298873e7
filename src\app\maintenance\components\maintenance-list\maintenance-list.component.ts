import {
  Component,
  OnInit,
  AfterViewInit,
  ViewChild,
  OnDestroy,
  Output,
  EventEmitter,
  ViewEncapsulation,
} from '@angular/core';
import { of, Subject } from 'rxjs';

import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { MatDialog } from '@angular/material/dialog';

import { NotificationsService } from '@shared/services/notifications.service';

import {
  GetSortOrder,
  GetSortOrderObject,
  ReplaceElementArray,
} from '@app/shared/utils/utils';
import { catchError, takeUntil } from 'rxjs/operators';
import { SharedVarService } from '@app/services/SharedVarService/shared-var-service.service';
import { typeAction } from '@app/shared/utils/enum';
import { DialogGliderInfoComponent } from '@app/shared/components/dialog-glider-information/dialog-glider-information.component';
import { GliderMaintenance } from '@app/core/interfaces/gliderMaintenance.interface';
import { GliderMaintenanceService } from '@app/services/gliderMaintenance/gliderMaintenance.service';
import { DialogMaintenanceCreateComponent } from '../dialog-maintenance-create/dialog-maintenance-create.component';
import { FleetService } from '@app/services/fleet/fleet.service';
import { Glider } from '@app/core/interfaces/glider.interace';
import { FormControl } from '@angular/forms';

@Component({
  selector: 'app-maintenance-list',
  templateUrl: './maintenance-list.component.html',
  styleUrls: ['./maintenance-list.component.css'],
  encapsulation: ViewEncapsulation.None,
})
export class MaintenanceListComponent
  implements OnInit, OnDestroy, AfterViewInit
{
  displayedColumns: string[] = [
    'glider',
    'due_date',
    'completed_date',
    'type',
    'notes',
    'post_check',
    'actions'
  ];
  dataSource = new MatTableDataSource<GliderMaintenance>();
  subject = new Subject<any>();
  data: GliderMaintenance[] = [];
  gliderFilter: Glider[] = [];
  gliderFilterFormControl: FormControl = new FormControl('');
  searchFieldFormControl: FormControl = new FormControl('');

  filterValues = {
    glider_id: '',
    search_term: '',
  };

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @Output() eventData = new EventEmitter<boolean>();

  constructor(
    private gliderMaintenanceService: GliderMaintenanceService,
    private fleetService: FleetService,
    public dialog: MatDialog,
    private notificationsAlerts: NotificationsService,
    private sharedVarService: SharedVarService
  ) {}

  ngOnInit(): void {
    this.sharedVarService.setValue('Maintenances');
    this.loadData();

    this.gliderMaintenanceService
      .listenDataSubjectMaintenance()
      .pipe(
        takeUntil(this.subject),
        catchError((err) => {
          this.notificationsAlerts.openSnackBar('Error', 'Ok');
          return of(err);
        })
      )
      .subscribe((res) => {
        if (this.gliderMaintenanceService.action === typeAction.created) {
          this.data.push(res);
          this.dataSource.data = this.data;
        } else if (this.gliderMaintenanceService.action === typeAction.updated) {
          this.data = ReplaceElementArray(this.data, res);
          this.dataSource.data = this.data;
        }
      });

      this.fleetService
      .getFleetList()
      .pipe(
        takeUntil(this.subject),
        catchError((err) => {
          this.notificationsAlerts.openSnackBar('Error', 'Ok');
          return of(err);
        })
      )
      .subscribe((res) => {
        this.gliderFilter = res.sort((a: Glider, b: Glider) => b.name.localeCompare(a.name));
      });

    this.fieldListener();

  }

  private createFilter(): (maintenance: GliderMaintenance, filter: string) => boolean {
    let filterFunction = function (maintenance: any, filter: any): boolean {
      let filters = JSON.parse(filter);

      let qualifies: boolean = true;

      if (filters.glider_id) {
        qualifies =
          maintenance.glider &&
          maintenance.glider.id == filters.glider_id;
      }

      if (filters.search_term) {
        qualifies =
          qualifies &&
          maintenance.notes &&
          maintenance.notes.toLowerCase().indexOf(filters.search_term.toLowerCase()) !==
            -1;
      }

      return qualifies;
    };

    return filterFunction;
  }

  private fieldListener() {
    this.gliderFilterFormControl.valueChanges.subscribe((glider_id) => {
      this.filterValues.glider_id = glider_id;
      this.dataSource.filter = JSON.stringify(this.filterValues);
    });

    this.searchFieldFormControl.valueChanges.subscribe((search) => {
      this.filterValues.search_term = search;
      this.dataSource.filter = JSON.stringify(this.filterValues);
    });
  }

  clearfilters(): void {
    this.gliderFilterFormControl.setValue('');
    this.searchFieldFormControl.setValue('');
    this.filterValues.glider_id = '';
    this.filterValues.search_term = '';
  }

  ngAfterViewInit(): void {
    this.dataSource.filterPredicate = this.createFilter();
    this.dataSource.paginator = this.paginator;
  }

  loadData(): void {
    this.gliderMaintenanceService
      .getMaintenanceList()
      .pipe(
        takeUntil(this.subject),
        catchError((err) => {
          this.notificationsAlerts.openSnackBar('Error', 'Ok');
          return of(err);
        })
      )
      .subscribe((res) => {
        this.data = this.dataSource.data = res;
        this.data.length
          ? this.eventData.emit(false)
          : this.eventData.emit(false);
      });
  }

  orderBy(column: string): void {
    this.data = this.dataSource.data = this.data.sort(GetSortOrder(column));
  }

  orderByObject(keyObject: string, column: string): void {
    this.data = this.dataSource.data = this.data.sort(
      GetSortOrderObject(keyObject, column)
    );
  }

  ngOnDestroy(): void {
    this.subject.complete();
    this.subject.unsubscribe();
  }

  openDialogCreate(): void {
    this.dialog.open(DialogMaintenanceCreateComponent, {
      height: 'auto',
      width: '600px',
      data: {
        typeAction: 'create',
      },
    });
  }

  openDialogEdit(row: GliderMaintenance): void {
    this.dialog.open(DialogMaintenanceCreateComponent, {
      height: 'auto',
      width: '600px',
      data: {
        data: row,
        typeAction: 'edit',
      },
    });
  }

  openDialogGliderInfo(row: GliderMaintenance): void {
    this.dialog.open(DialogGliderInfoComponent, {
      height: 'auto',
      width: '600px',
      data: row.glider,
    });
  }
}
