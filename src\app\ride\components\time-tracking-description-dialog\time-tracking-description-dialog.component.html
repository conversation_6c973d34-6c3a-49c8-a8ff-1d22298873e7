<h2 mat-dialog-title class="dialog-title">Start Time Tracking</h2>
<div mat-dialog-content>
  <p class="dialog-subtitle">Enter a description for this time tracking session:</p>
  <mat-form-field appearance="outline" style="width: 100%;">
    <mat-label>Description</mat-label>
    <textarea matInput [formControl]="descriptionControl" placeholder="What are you working on?"></textarea>
    <mat-hint>Optional: If left empty, the ride ID will be used</mat-hint>
    <mat-error *ngIf="descriptionControl.hasError('maxlength')">
      Description cannot exceed 255 characters
    </mat-error>
  </mat-form-field>
</div>
<div mat-dialog-actions align="end" class="dialog-actions">
  <button mat-stroked-button (click)="onCancel()">Cancel</button>
  <button mat-raised-button color="primary" (click)="onSubmit()" [disabled]="!descriptionControl.valid">Start Tracking</button>
</div>
