{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"delivery-glider": {"projectType": "application", "schematics": {"@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@ngx-env/builder:browser", "options": {"outputPath": "dist/delivery-glider", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/custom-theme.scss", "node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "node_modules/bootstrap/dist/css/bootstrap.min.css", "./node_modules/intl-tel-input/build/css/intlTelInput.css", "src/styles.css"], "scripts": [], "vendorChunk": true, "extractLicenses": false, "buildOptimizer": false, "sourceMap": true, "optimization": false, "namedChunks": true}, "configurations": {"staging": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.staging.ts"}]}, "development": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.development.ts"}]}, "production": {"optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "3mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "4kb"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "delivery-glider:build"}, "configurations": {"production": {"browserTarget": "delivery-glider:build:production"}, "staging": {"browserTarget": "delivery-glider:build:staging"}, "development": {"browserTarget": "delivery-glider:build:development"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "delivery-glider:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon.ico", "src/assets"], "styles": ["./node_modules/@angular/material/prebuilt-themes/indigo-pink.css", "src/styles.css"], "scripts": []}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["tsconfig.app.json", "tsconfig.spec.json", "e2e/tsconfig.json"], "exclude": ["**/node_modules/**"]}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "delivery-glider:serve"}, "configurations": {"production": {"devServerTarget": "delivery-glider:serve:production"}}}}}}, "defaultProject": "delivery-glider", "cli": {"analytics": false}}