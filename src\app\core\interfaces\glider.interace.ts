import {SoftwareVersionType} from '@core/interfaces/softwareversion.interface';

export interface Glider {
  desiredFtsRaspiSoftwareVersion: SoftwareVersion;
  desiredFtsPixhawkSoftwareVersion: SoftwareVersion;
  desiredAutopilotSoftwareVersion: SoftwareVersion;
  desiredJetsonSoftwareVersion: SoftwareVersion;
  jetsonSoftwareVersion: SoftwareVersion;
  autopilotSoftwareVersion: SoftwareVersion;
  ftsRaspiSoftwareVersion: SoftwareVersion;
  ftsPixhawkSoftwareVersion: SoftwareVersion;
  id: number;
  line: string;
  generation: string;
  number: string;
  name: string;
  pixhawkUuid: string;
  gliderStatus: GliderStatus;
  gliderMode: {
      id: number;
      createdAt: string; // ISO date string
      updatedAt: string; // ISO date string
      name: string;
      description: string;
  };
  inUse: boolean;
  status: GliderStatus;
  autopilotSWVersion: {
      id: number;
      createdAt: string; // ISO date string
      updatedAt: string; // ISO date string
      name: string;
  };
  jetsonSWVersion: {
      id: number;
      createdAt: string; // ISO date string
      updatedAt: string; // ISO date string
      name: string;
  };
  company: {
      id: number;
      createdAt: string; // ISO date string
      updatedAt: string; // ISO date string
      name: string;
  };
  region: {
      id: number;
      createdAt: string; // ISO date string
      updatedAt: string; // ISO date string
      country: string;
      description: string;
      supportPhoneCountryCode: string;
      supportPhoneNumber: string;
      supportEmail: string;
  };
  vpnIp: string;
  vpnNetworkId: string | null;
  manufacturingDate: string | null; // ISO date string or null
  registrationCode: string;
  registrationComplete: boolean;
  totalFlightTimeInSeconds: number | null;
  totalFlightTimeSinceLastMaintenanceInSeconds: number | null;
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
}

export interface SoftwareVersion {
  id: number;
  name: string;
  createdAt: string;
  updatedAt: string;
  softwareVersionType: SoftwareVersionType;
}

export interface GliderStatus {
  id: number;
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  name: string;
  colorHexcode: string;
}

export interface UpdateStatusPayload {
  gliderName: string;
  status: string;
}
