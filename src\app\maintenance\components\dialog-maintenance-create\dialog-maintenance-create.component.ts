import {Component, OnInit, Inject, OnD<PERSON>roy} from '@angular/core';
import {Subject, of, Observable} from 'rxjs';
import {FormControl, FormGroup, Validators} from '@angular/forms';

import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {NotificationsService} from '@shared/services/notifications.service';

import {catchError, map, startWith, takeUntil} from 'rxjs/operators';
import {typeAction} from '@app/shared/utils/enum';
import {Glider} from '@app/core/interfaces/glider.interace';
import {GliderMaintenance} from '@app/core/interfaces/gliderMaintenance.interface';
import {GliderMaintenanceService} from '@app/services/gliderMaintenance/gliderMaintenance.service';
import {FleetService} from '@app/services/fleet/fleet.service';
import * as moment from 'moment';
import {maintenanceType} from '@app/core/interfaces/gliderMaintenance.interface';
import {UserService} from '@services/user/user.service';
import {UserData} from '@core/interfaces/userdata.interface';

interface Data {
  typeAction: string;
  data: GliderMaintenance;
}

@Component({
  selector: 'app-dialog-maintenance-create',
  templateUrl: './dialog-maintenance-create.component.html',
  styleUrls: ['./dialog-maintenance-create.component.css'],
})
export class DialogMaintenanceCreateComponent implements OnInit, OnDestroy {
  form!: FormGroup;
  subject = new Subject<any>();
  edit = false;
  filteredOptions: Observable<Glider[]>;
  dataGlider: Glider[];
  maintenanceTypes: maintenanceType[];

  dueDate = new Date();
  completedDate = new Date();
  keycloakUsers: any[] = [];

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: Data,
    private gliderMaintenanceService: GliderMaintenanceService,
    private fleetService: FleetService,
    private notificationsAlerts: NotificationsService,
    public dialogRef: MatDialogRef<DialogMaintenanceCreateComponent>,
    private userService: UserService,
  ) {

    if (this.data.typeAction === 'edit' && this.data.data) {
      this.edit = true;
    }

    this.form = new FormGroup({
      id: new FormControl(this.data.data?.id),
      glider: new FormControl(this.data.data?.glider, [Validators.required]),
      due_date: new FormControl(this.data.data?.dueDate, [Validators.required]),
      completed_date: new FormControl(this.data.data?.completedDate),
      maintenance_type: new FormControl(
        this.data.data?.maintenanceType?.id ?? this.data.data?.maintenanceTypeId,
        [Validators.required]
      ),
      notes: new FormControl(this.data.data?.notes),
      postMaintenanceChecklistDone: new FormControl(
        this.data.data?.postMaintenanceChecklistDone || false
      ),
      maintenance_staff: new FormControl(
        this.data.data?.maintenanceStaff ?? null,
        [Validators.required]
      ),
      scheduled: new FormControl(this.data.data?.isScheduled || false)
    });
  }

  ngOnInit(): void {
    this.userService.getUserList()
      .pipe(
        takeUntil(this.subject),
        catchError((err) => {
          this.notificationsAlerts.openSnackBar('Error loading staff users', 'Ok');
          return of([] as UserData[]);
        })
      )
      .subscribe((users: UserData[]) => {
        this.keycloakUsers = users;
      });
    this.fleetService
      .getFleetList()
      .pipe(
        takeUntil(this.subject),
        catchError((err) => {
          this.notificationsAlerts.openSnackBar('Error', 'Ok');
          return of(err);
        })
      )
      .subscribe((res) => {
        this.dataGlider = res;

        this.filteredOptions = this.form.controls.glider.valueChanges.pipe(
          startWith(''),
          map((value) => {
            const note =
              typeof value === 'string' ? value : value?.name || value?.id;
            return note
              ? this._filter(note as string)
              : this.dataGlider?.slice();
          })
        );
      });

    this.gliderMaintenanceService
      .getMaintenanceTypes()
      .pipe(
        takeUntil(this.subject),
        catchError((err) => {
          this.notificationsAlerts.openSnackBar('Error', 'Ok');
          return of(err);
        })
      )
      .subscribe((res) => {
        this.maintenanceTypes = res;

        this.filteredOptions = this.form.controls.glider.valueChanges.pipe(
          startWith(''),
          map((value) => {
            const note =
              typeof value === 'string' ? value : value?.name || value?.id;
            return note
              ? this._filter(note as string)
              : this.dataGlider?.slice();
          })
        );
      });
  }

  displayFn(glider: Glider): string {
    return glider && glider.name ? glider.name : '';
  }

  private _filter(value: string): Glider[] {
    const filterValue = value.toLowerCase();

    return this.dataGlider.filter(
      (glider) =>
        glider.name.toLowerCase().includes(filterValue) ||
        glider.id.toString().includes(filterValue)
    );
  }

  save(): void {
    if (this.form.invalid) {
      this.form.markAllAsTouched();
      return;
    }
    if (!this.form.invalid) {
      const dueDate = this.form.value.due_date 
        ? moment(this.form.value.due_date).hours(12).minutes(0).seconds(0).toISOString()
        : null;
      
      const completedDate = this.form.value.completed_date
        ? moment(this.form.value.completed_date).hours(12).minutes(0).seconds(0).toISOString()
        : null;

      const body = {
        gliderId: this.form.value.glider.id,
        dueDate,
        dueDateTime: dueDate,
        completedDate,
        completedDateTime: completedDate,
        isScheduled: this.form.value.scheduled || false,
        notes: this.form.value.notes,
        maintenanceTypeId: this.form.value.maintenance_type,
        mailboxId: this.form.value.mailboxId || null,
        postMaintenanceChecklistDone: this.form.value.postMaintenanceChecklistDone,
        maintenanceStaff: this.form.value.maintenance_staff
      };

      if (!this.edit) {
        this.gliderMaintenanceService
          .createMaintenance(body)
          .pipe(
            takeUntil(this.subject),
            catchError((err) => {
              if (err.status === 403) {
                this.notificationsAlerts.openSnackBar(
                  'This user is not authorized to do maintenance on this glider model',
                  'Close'
                );
                this.dialogRef.close();
              } else {
                this.notificationsAlerts.openSnackBar('Error creating maintenance: ' + (err.error?.message || 'Unknown error'), 'Ok');
              }
              return of(err);
            })
          )
          .subscribe((res) => {
            if (res && res.status === 403) {
              this.notificationsAlerts.openSnackBar(
                'This user is not authorized to do maintenance on this glider model',
                'Close'
              );
              this.dialogRef.close();
              return;
            }

            if (res.id) {
              this.gliderMaintenanceService.notifyDataSubject(
                res,
                typeAction.created
              );
              this.dialogRef.close();
              this.notificationsAlerts.openSnackBar(`Maintenance for Glider "${this.form.value.glider.name}"
              created successfully!`, 'Close');
            }
          });
      } else {
        this.gliderMaintenanceService
          .updateMaintenance(this.data.data.id, body)
          .pipe(
            takeUntil(this.subject),
            catchError((err) => {
              this.notificationsAlerts.openSnackBar('Error', 'Ok');
              return of(err);
            })
          )
          .subscribe((res) => {
            if (res) {
              this.gliderMaintenanceService.notifyDataSubject(res, typeAction.updated);
              this.dialogRef.close();
              this.notificationsAlerts.openSnackBar(`Maintenance for Glider "${this.form.value.glider.name}" updated successfully!`,
                'Close');
            }
          });
      }
    }
  }

  updateDueDate(event: any) {
    this.dueDate = event.target.valueAsDate;
  }

  updateCompletedDate(event: any) {
    this.completedDate = event.target.valueAsDate;
  }

  get formDialog(): any {
    return this.form.controls;
  }

  ngOnDestroy(): void {
    this.subject.complete();
    this.subject.unsubscribe();
  }
}
