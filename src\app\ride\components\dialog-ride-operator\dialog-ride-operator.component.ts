import { Component, OnInit, Inject } from '@angular/core';
import { Subject, of, Observable } from 'rxjs';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { catchError, map, startWith, takeUntil } from 'rxjs/operators';

import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { NotificationsService } from '@shared/services/notifications.service';

import { RideService } from '@app/services/ride/ride.service';
import { Ride } from '@app/core/interfaces/ride.interface';
import { UserData } from '@app/core/interfaces/userdata.interface';
import { HttpParams } from '@angular/common/http';
import { UserService } from '@app/services/user/user.service';
import { typeAction } from '@app/shared/utils/enum';

@Component({
  selector: 'app-dialog-ride-operator',
  templateUrl: './dialog-ride-operator.component.html',
  styleUrls: ['./dialog-ride-operator.component.css'],
})
export class DialogRideOperatorComponent implements OnInit {
  form!: FormGroup;
  subject = new Subject<any>();
  filteredOptions: Observable<UserData[]>;

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: Ride,
    @Inject(MAT_DIALOG_DATA) public dataUser: UserData[],
    private rideService: RideService,
    private userService: UserService,
    private notificationsAlerts: NotificationsService,
    public dialogRef: MatDialogRef<DialogRideOperatorComponent>
  ) { }

  ngOnInit(): void {
    this.form = new FormGroup({
      ride_id: new FormControl(this.data.id, [Validators.required]),
      user: new FormControl(this.data.operator_id, [Validators.required]),
    });

    this.loadData();
  }

  loadData(): void {
    const userParams = new HttpParams().append('user[jedsetter_and_above]', 1);

    this.userService
      .getUserList(userParams)
      .pipe(
        takeUntil(this.subject),
        catchError((err) => {
          this.notificationsAlerts.openSnackBar('Error', 'Ok');
          return of(err);
        })
      )
      .subscribe((res) => {
        this.dataUser = res;

        this.filteredOptions = this.form.controls.user.valueChanges.pipe(
          startWith(''),
          map((value) => {
            const note =
              typeof value === 'string'
                ? value
                : value?.company_name || value?.first_name || value?.id;
            return note ? this._filter(note as string) : this.dataUser.slice();
          })
        );
      });
  }

  displayFn(user: UserData): string {
    return user.email;
  }

  private _filter(value: string): UserData[] {
    const filterValue = value.toLowerCase();

    return this.dataUser.filter(
      (user) =>
        user.company_name?.toLowerCase().includes(filterValue) ||
        user.first_name.toLowerCase().includes(filterValue) ||
        user.id.toString().includes(filterValue)
    );
  }

  accept(): void {
    this.dialogRef.close(true);
  }

  cancel(): void {
    this.dialogRef.close(false);
  }

  update(): void {
    if (!this.form.invalid && this.form.value.user.id) {
      const body = {
          operator_id: this.form.value.user.id,
          ride_status_id: null,
          has_package: null,
          package_description: null,
          departure_time: null,
          arrival_time: null,
          glider_id: null,
          glider_name: null,
      };

      this.rideService
        .updateRide(this.data.id, body)
        .pipe(
          takeUntil(this.subject),
          catchError((err) => {
            this.notificationsAlerts.openSnackBar('Error', 'Ok');
            return of(err);
          })
        )
        .subscribe((res) => {
          if (res.id) {
            this.rideService.notifyDataSubject(res, typeAction.updated);
            this.dialogRef.close();
          }
          this.notificationsAlerts.openSnackBar("Assigned to operator", 'Ok');
        });
    }
  }

  get formDialog(): any {
    return this.form.controls;
  }

  ngOnDestroy(): void {
    this.subject.complete();
    this.subject.unsubscribe();
  }
}
