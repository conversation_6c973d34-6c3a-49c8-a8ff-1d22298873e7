<mat-sidenav-container class="sidenav-container" [class.sidebar-collapsed]="isCollapsed">
  <mat-sidenav
    #drawer
    class="sidenav"
    [class.collapsed]="isCollapsed"
    fixedInViewport
    [attr.role]="(isHandset$ | async) ? 'dialog' : 'navigation'"
    mode="side"
    [opened]="true"
  >
    <mat-toolbar class="sidebar-toolbar" [class.collapsed]="isCollapsed">
      <div class="toolbar-content">
        <img
          *ngIf="!isCollapsed"
          src="/assets/icons/logo_v1.svg"
          width="120px"
          alt=""
          class="img-fluid d-block mt-5"
        />
        <div *ngIf="isCollapsed" class="collapsed-logo">
          J
        </div>
      </div>
    </mat-toolbar>

    <button
      class="floating-toggle-btn"
      [class.collapsed]="isCollapsed"
      (click)="toggleSidebar()"
      [attr.aria-label]="isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'"
      [matTooltip]="isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'"
      matTooltipPosition="right">
      <div class="toggle-icon" [class.collapsed]="isCollapsed">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
          <path class="arrow-path" d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
      </div>
    </button>

    <mat-nav-list class="mt-5" [class.collapsed]="isCollapsed">
      <a
        mat-list-item
        href="{{urlMicroserviceApp}}/software"
        class="navigate-item"
        [matTooltip]="isCollapsed ? 'Software Versions' : ''"
        matTooltipPosition="right"
      >
        <span
          [inlineSVG]="'./assets/icons/software.svg'"
          class="icon-navigate"
        >
        </span>
        <span *ngIf="!isCollapsed" class="nav-text">&nbsp; Software Versions</span>
      </a>
      <a
        mat-list-item
        href="{{urlMicroserviceApp}}/routes"
        class="navigate-item"
        [matTooltip]="isCollapsed ? 'Routes' : ''"
        matTooltipPosition="right"
      >
        <span
          [inlineSVG]="'./assets/icons/routes.svg'"
          class="icon-navigate"
        >
        </span>
        <span *ngIf="!isCollapsed" class="nav-text">&nbsp; Routes</span>
      </a>
      <a
        mat-list-item
        href="{{urlMicroserviceApp}}/shifts"
        class="navigate-item"
        [matTooltip]="isCollapsed ? 'Time Tracking History' : ''"
        matTooltipPosition="right"
      >
        <span
          [inlineSVG]="'./assets/icons/time-tracking.svg'"
          class="icon-navigate"
        >
        </span>
        <span *ngIf="!isCollapsed" class="nav-text">&nbsp; Time Tracking History</span>
      </a>
      <a
        mat-list-item
        href="{{urlMicroserviceApp}}/calendar"
        class="navigate-item"
        [matTooltip]="isCollapsed ? 'Pilot Schedule' : ''"
        matTooltipPosition="right"
      >
        <span
          [inlineSVG]="'./assets/icons/calendar.svg'"
          class="icon-navigate"
        >
        </span>
        <span *ngIf="!isCollapsed" class="nav-text">&nbsp; Pilot Schedule</span>
      </a>
      <a
        mat-list-item
        href="{{urlMicroserviceApp}}/batch-upload"
        class="navigate-item"
        [matTooltip]="isCollapsed ? 'Batch Upload Flight Schedule' : ''"
        matTooltipPosition="right"
      >
        <span
          [inlineSVG]="'./assets/icons/batch-upload.svg'"
          class="icon-navigate"
        >
        </span>
        <span *ngIf="!isCollapsed" class="nav-text">&nbsp; Batch Upload Flight Schedule</span>
      </a>
      <a
        *ngIf="!hideTabs"
        mat-list-item
        routerLink="/locations"
        routerLinkActive="navigate-item-active"
        class="navigate-item"
        [matTooltip]="isCollapsed ? 'Locations' : ''"
        matTooltipPosition="right"
      >
        <span
          [inlineSVG]="'./assets/icons/locations.svg'"
          class="icon-navigate"
        >
        </span>
        <span *ngIf="!isCollapsed" class="nav-text">&nbsp; Locations</span>
      </a>
      <a
        mat-list-item
        routerLink="/fleets"
        routerLinkActive="navigate-item-active"
        class="navigate-item"
        [matTooltip]="isCollapsed ? 'Fleet' : ''"
        matTooltipPosition="right"
      >
        <span
          [inlineSVG]="'./assets/icons/fleet.svg'"
          class="icon-navigate"
        >
        </span>
        <span *ngIf="!isCollapsed" class="nav-text">&nbsp; Fleet</span>
      </a>
      <a
        mat-list-item
        routerLink="/maintenances"
        routerLinkActive="navigate-item-active"
        class="navigate-item"
        [matTooltip]="isCollapsed ? 'Maintenances' : ''"
        matTooltipPosition="right"
      >
        <span
          [inlineSVG]="'./assets/icons/maintenance.svg'"
          class="icon-navigate"
        >
        </span>
        <span *ngIf="!isCollapsed" class="nav-text">&nbsp; Maintenances</span>
      </a>
      <a
        *ngIf="!hideTabs"
        mat-list-item
        routerLink="/rides"
        routerLinkActive="navigate-item-active"
        class="navigate-item"
        [matTooltip]="isCollapsed ? 'Rides' : ''"
        matTooltipPosition="right"
      >
        <span
          [inlineSVG]="'./assets/icons/rides.svg'"
          class="icon-navigate"
        >
        </span>
        <span *ngIf="!isCollapsed" class="nav-text">&nbsp; Rides</span>
      </a>

      <a
        mat-list-item
        href={{urlIpConnector}}
        target="_blank"
        class="navigate-item"
        [matTooltip]="isCollapsed ? 'IP Connector' : ''"
        matTooltipPosition="right"
      >
        <span
          [inlineSVG]="'./assets/icons/external-link.svg'"
          class="icon-navigate"
        >
        </span>
        <span *ngIf="!isCollapsed" class="nav-text">&nbsp; IP Connector</span>
      </a>
    </mat-nav-list>
  </mat-sidenav>
  <mat-sidenav-content [class.sidebar-collapsed]="isCollapsed">
    <mat-toolbar color="primary" class="bg-toolbar col-12">
      <button
        type="button"
        aria-label="Toggle sidenav"
        mat-icon-button
        (click)="drawer.toggle()"
        *ngIf="isHandset$ | async"
      >
        <mat-icon aria-label="Side nav toggle icon">menu</mat-icon>
      </button>
      <span [ngClass]="{ 'cursor-pointer': isDetails }" (click)="back()">
        <mat-icon *ngIf="isDetails">
          <span class="material-icons vertical-align-sub">
            keyboard_arrow_left
          </span>
        </mat-icon>
        {{ title }}
      </span>
      <div class="d-flex justify-content-end align-middle ms-auto">
        <mat-chip-list aria-label="Fish selection">
          <mat-chip class="btn-green-dark user-nav" selected>{{
              letters
            }}
          </mat-chip>
        </mat-chip-list>
        <button mat-button [matMenuTriggerFor]="menu">
          {{ user }}
          <mat-icon>
            <span class="material-icons"> keyboard_arrow_down </span>
          </mat-icon>
        </button>
        <mat-menu #menu="matMenu">
          <a
            href={{getUserAccountConsoleUrl()}}
            target="_blank"
          >
            <button
              mat-menu-item
              style="width: 250px"
            >

              <mat-icon>
                <span class="material-icons icon-green"> account_box </span>
              </mat-icon>
              Manage Account
            </button>
          </a>
          <hr/>

          <button mat-menu-item disabled class="text-center">
            version {{ version }}
          </button>
        </mat-menu>
      </div>
    </mat-toolbar>
    <!-- Add Content Here -->
    <router-outlet></router-outlet>
    <!-- <div class="content">
    </div> -->
  </mat-sidenav-content>
</mat-sidenav-container>
