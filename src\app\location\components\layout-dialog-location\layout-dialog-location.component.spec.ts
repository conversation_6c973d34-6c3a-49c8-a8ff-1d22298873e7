import { ComponentFixture, TestBed } from '@angular/core/testing';

import { LayoutDialogLocationComponent } from './layout-dialog-location.component';

describe('LayoutDialogLocationComponent', () => {
  let component: LayoutDialogLocationComponent;
  let fixture: ComponentFixture<LayoutDialogLocationComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [LayoutDialogLocationComponent],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(LayoutDialogLocationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
