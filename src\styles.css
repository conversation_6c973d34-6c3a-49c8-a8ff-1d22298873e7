/* Colors */

/*
  primary: #66B245
  secondary: #447A2D
  secondary 2: #66B2454C
  secondary 3: #DBDBDB
 */

body {
  margin: 0 !important;
  background-color: #f3f3f8;
  overflow: hidden;
  font: 600 "Open Sans", "Helvetica Neue", sans-serif !important;
  font-family: "Open Sans", sans-serif !important;
}

.mat-body,
.mat-body-1,
.mat-typography {
  font: 400 14px/20px "Open Sans", "Helvetica Neue", sans-serif !important;
}

.mat-h4,
.mat-subheading-1,
.mat-typography h4 {
  font: 400 15px/24px "Open Sans", "Helvetica Neue", sans-serif !important;
}

.mat-toolbar,
.mat-toolbar h1,
.mat-toolbar h2,
.mat-toolbar h3,
.mat-toolbar h4,
.mat-toolbar h5,
.mat-toolbar h6,
.mat-h2,
.mat-title,
.mat-typography h2 {
  font: 500 20px/32px "Open Sans", "Helvetica Neue", sans-serif !important;
}

.mat-button,
.mat-raised-button,
.mat-icon-button,
.mat-stroked-button,
.mat-flat-button,
.mat-fab,
.mat-mini-fab,
.mat-list-item,
.mat-table,
.mat-select,
.mat-paginator,
.mat-paginator-page-size .mat-select-trigger,
.mat-tab-label,
.mat-tab-link,
.mat-option {
  font-family: "Open Sans", "Helvetica Neue", sans-serif;
}

::-webkit-scrollbar {
  width: 7px;
  border-radius: 50px;
  height: 4px !important;
}

/* Track */
::-webkit-scrollbar-track {
  background: white;
  border-radius: 50px !important;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #aeaeae;
  border-radius: 50px;
  height: 2px !important;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #447a2d;
}

html,
body {
  height: 100%;
}

.layout-style {
  padding: 24px;
  background-color: #fff;
}

/* Tables */
table {
  width: 100% !important;
}

.table-container {
  /* width:100% !important; */
  overflow-x: auto;
}

.photo {
  width: 100px;
  height: 100px;
  margin: 10px;
  border-radius: 20px;
  cursor: pointer;
}

.collapse-separate {
  border-collapse: separate;
}

.headerTable {
  color: white;
  background-color: #447a2d;
  width: 100%;
}

.headerTable th:first-child {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
  border: solid 1px #447a2d;
}

.headerTable th:last-child {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
  border: solid 1px #447a2d;
}

.headerText {
  font-size: 15px;
  color: white;
  padding: 2% 0;
}

.sub-content {
  width: 95%;
  margin-left: 2.5%;
  margin-top: 30px;
  margin-bottom: 30px;
  border-radius: 10px;
}

.btn-detail-map {
  background-color: #66b245;
  color: white;
  font-weight: lighter;
}

.btn-detail-hover:hover {
  color: #66b245;
  text-decoration: underline;
  cursor: pointer;
}

.tab-content {
  /* padding-top: 30px; */
}

.mat-column-address {
  width: 10% !important;
}

/* BTN COLORS */

.btn-green {
  background-color: #66b245 !important;
  color: white !important;
}

.btn-green-olivine {
  background-color: #92c87b !important;
  color: white !important;
}

.btn-green-dark {
  background-color: #447a2d !important;
  color: white !important;
}

.btn-red {
  background-color: #f44336 !important;
  color: white !important;
}

.btn-cancel {
  border-color: #66b245 !important;
  color: #66b245 !important;
  border: 1px solid !important;
}

.btn-purple {
  background-color: #b784f4 !important;
  color: white !important;
}

.btn-gray {
  background-color: #c5c5c5 !important;
  color: white !important;
}

.btn-dark-gray {
  background-color: #777d76 !important;
  color: white !important;
}

.btn-orange {
  background-color: #f3a16b !important;
  color: white !important;
}

.btn-green-mountain {
  background-color: #61bcaa !important;
  color: white !important;
}

.btn-sapphire {
  background-color: #6a74b6 !important;
  color: white !important;
}

.btn-yellow {
  background-color: #f8df89 !important;
  color: white !important;
}

/* ICONS */
.actions-only-icons {
  font-weight: bold;
  cursor: pointer;
}

.actions-only-icons.mlr {
  margin: 0 6px;
}

.icon-yellow {
  color: #f8df89;
}

.icon-blue {
  color: #3f51b5;
}

.icon-black {
  color: #000;
}

.icon-sky-blue {
  color: #61a9ed;
}

.icon-gray {
  color: #c5c5c5;
}

.icon-green {
  color: #66b245;
}

.icon-red {
  color: #f44336;
}

.icon-orange {
  color: #ffa247;
}

.icon-purple {
  color: #b784f4;
}

/* TOOLTIPS */
.basic-tooltip {
  background-color: white;
  color: black !important;
  font-size: 1em;
  box-shadow:
    0 3px 5px -1px rgb(0 0 0 / 20%),
    0 5px 8px 0 rgb(0 0 0 / 14%),
    0 1px 14px 0 rgb(0 0 0 / 12%);
}

/* SLIDER TOGGLE */
.mat-slide-toggle.mat-checked .mat-slide-toggle-thumb {
  background-color: #66b245;
}

.mat-slide-toggle.mat-checked .mat-slide-toggle-bar {
  background-color: #447a2d;
}

/* TEXT COLORS */
.text-success {
  color: #66b245 !important;
}

.text-danger {
  color: #f44336 !important;
}

.custom-mat-tab .mat-tab-label-active {
  color: #447a2d !important;
  background: rgb(0 0 0 / 4%) !important;
}

.btn-detail {
  color: #66b245;
  text-decoration: underline;
}

.btn-detail span {
  cursor: pointer;
}

/* TABS */
.form-tab .mat-tab-header {
  margin-bottom: 12px;
}

.form-tab .mat-tab-labels .mat-tab-label {
  text-align: left;
  padding: 8px 0;
  justify-content: left;
  height: auto;
}

.form-tab .mat-tab-labels .mat-tab-label-active {
  color: #66b245 !important;
}

.custom-mat-tab .mat-tab-nav-bar,
.custom-mat-tab .mat-tab-header {
  border-bottom: none;
  margin-bottom: 19px;
}

.custom-mat-tab .mat-tab-label {
  min-width: 0 !important;
  border-radius: 8px;
  height: 36px !important;
  color: black;
}

/* Forms */
form label {
  font-weight: 500;
}

/* OTHER STYLES */
.cursor-pointer {
  cursor: pointer !important;
}

.mat-ink-bar {
  background: #447a2d !important;
}

.custom-mat-tab .mat-ink-bar {
  display: none;
}

.warn {
  background-color: #f44336 !important;
  color: white !important;
}

.basic {
  background-color: #aeaeae !important;
  color: white !important;
}

.purple {
  background-color: #3f51b5 !important;
  color: white !important;
}

.user-nav {
  border-radius: 7px !important;
}

.input-style,
.input-style-form {
  background: #f4f6fd !important;
  border: none;
  border-radius: 8px;
  padding: 0.375rem 0.75rem !important;
  color: rgb(0 0 0 / 87%) !important;
}

.input-style-form.input-flags {
  margin-left: 5rem !important;
  height: 36px;
}

.iti {
  margin-top: 0.5rem;
}

.input-style {
  width: 15% !important;
}

.input-style div.mat-input-wrapper,
.input-style div.mat-form-field-flex,
.input-style div.mat-form-field-wrapper,
.input-style div.mat-form-field-infix,
.input-style div.mat-form-field-label-wrapper,
.input-style div.mat-form-field-prefix {
  padding: 0;
  border: 0;
  align-self: center;
}

.input-style div.mat-form-field-prefix {
  color: #afafaf;
}

.input-style div.mat-form-field-infix {
  margin-top: 2px;
}

.input-style div.mat-form-field-flex,
.input-style input {
  height: 100%;
}

.input-style div.mat-form-field-underline {
  display: none;
}

.input-style .select-icon {
  color: #000;
}

.input-style .mat-select-trigger {
  height: 100%;
}

.input-style .mat-select-value {
  vertical-align: middle;
}

.input-style-autocomplete {
  width: 100%;
  padding: 0 0.75em;
}

.mat-autocomplete .mat-icon {
  color: #afafaf;
}

.mat-autocomplete .mat-form-field-infix {
  border-top: 0.4em solid transparent !important;
}

.mat-form-field-infix {
  width: auto !important;
}

mat-select.input-style.form-control:focus {
  background-color: #f4f6fd;
  border: 0;
  box-shadow: 0 0 0 0;
}

.btn-style {
  margin: 0 !important;
}

.mat-progress-bar-fill {
  background-color: #66b245 !important;
}

.mat-progress-bar-fill::after {
  background-color: #66b245 !important;
}

.tab-content .mat-dialog-title {
  margin-top: 16px !important;
}

.notification-template {
  background-color: #eee;
  color: #000;
  border-radius: 12px 12px 12px 0;
  margin-bottom: 16px;
  padding: 12px;
}

.notification-template p {
  font-weight: bold;
}

.notification-template .date {
  color: #afafaf;
}

.mr-3 {
  margin-right: 1rem !important;
}

.mb-3 {
  margin-bottom: 1rem !important;
}

.align-mid {
  vertical-align: middle !important;
}

.w-100 {
  width: 100% !important;
}

.text-right {
  text-align: right !important;
}

.visibility-hidden {
  visibility: hidden !important;
}

.vertical-align-sub {
  vertical-align: sub !important;
}

.btn-action {
  margin: 5px !important;
  min-width: 36px !important;
  padding: 0 !important;
}

.tab-custom-overflow .mat-tab-body-wrapper {
  overflow: visible !important;
}

.tab-custom-overflow .mat-tab-body-active {
  overflow: visible !important;
}

.tab-custom-overflow .mat-tab-body-content {
  overflow: visible !important;
}

.mat-paginator-sticky {
  bottom: 0;
  position: sticky;
  z-index: 10;
}

.image--cover {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  margin: 20px;
  object-fit: cover;
  object-position: center right;
}
