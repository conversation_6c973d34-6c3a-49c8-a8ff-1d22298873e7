<div class="layout-container" [class.sidebar-collapsed]="isCollapsed">
    <div class="aside" [class.collapsed]="isCollapsed">
        <app-aside-menu [isCollapsed]="isCollapsed" (toggleSidebar)="toggleSidebar()"></app-aside-menu>
    </div>



    <div class="container" [ngStyle]="{'width': screenWidth}">
        <div class="topbar col-12">
            {{title}}
            <div class="d-flex justify-content-end align-middle ms-auto">
                <mat-chip-list aria-label="Fish selection">
                    <mat-chip class="btn-green-dark user-nav" selected>AG</mat-chip>
                </mat-chip-list>
                <button mat-button [matMenuTriggerFor]="menu">George
                    <mat-icon>
                        <span class="material-icons">
                            keyboard_arrow_down
                        </span>
                    </mat-icon>
                </button>
                <mat-menu #menu="matMenu">
                    <button mat-menu-item [routerLink]="['/login']">
                        <mat-icon>
                            <span class="material-icons icon-green">
                            logout
                            </span>
                        </mat-icon>
                        Logout
                    </button>
                </mat-menu>
            </div>
        </div>
        <div class="content" [ngStyle]="{'max-height' : screenHeight}">
            <router-outlet></router-outlet>
        </div>
    </div>
</div>

