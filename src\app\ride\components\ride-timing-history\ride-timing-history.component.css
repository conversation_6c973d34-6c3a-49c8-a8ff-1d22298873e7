.timing-history-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  width: 100%;
  max-width: 850px;
  max-height: 90vh;
  overflow: hidden;
  padding: 0;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 99999;
}

.timing-history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eee;
  background: #f8f9fa;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.timing-history-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.timing-history-summary {
  color: #666;
  font-size: 14px;
  margin-left: 16px;
}

.close-button {
  margin-left: auto;
}

.timing-history-content {
  width: 100%;
  box-sizing: border-box;
  max-height: calc(90vh - 80px);
  overflow: auto;
  position: relative;
  flex: 1;
}

.no-timings {
  color: #aaa;
  text-align: center;
  padding: 20px 0;
}

.no-timings p {
  margin: 0;
  font-size: 14px;
}

.timing-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0 5px;
  margin-top: 10px;
  table-layout: fixed;
  min-width: 750px;
}

.timing-table th {
  text-align: left;
  padding: 10px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
  font-weight: 500;
  font-size: 13px;
}

.timing-table td {
  padding: 8px 10px;
  border-bottom: 1px solid #eee;
  vertical-align: middle;
  word-wrap: break-word;
  overflow-wrap: break-word;
  position: relative;
}

.timing-row {
  transition: background-color 0.2s ease;
}

.timing-row:hover {
  background-color: #f8f8f8;
}

.server-row {
  border-left: 3px solid #2196f3;
}

.timing-row:nth-child(even) {
  background-color: #fafafa;
}

.timing-row:nth-child(even):hover {
  background-color: #f5f5f5;
}

.duration-cell {
  width: 160px!important;
  min-width: 160px;
}

.status-cell {
  width: 100px;
  text-align: right;
}

.edit-container {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 5px 0;
  position: relative;
  z-index: 10;
}

.edit-buttons {
  display: flex;
  gap: 4px;
  position: absolute;
  right: -70px;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  padding: 2px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.edit-buttons button {
  width: 30px;
  height: 30px;
  line-height: 30px;
}

.edit-buttons mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
  line-height: 18px;
}

.description-tooltip {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  margin-left: 4px;
}

.description-tooltip mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
  color: #757575;
}

.duration-value {
  cursor: pointer;
  text-decoration: none;
  font-family: 'Roboto Mono', monospace;
  background-color: rgba(0, 0, 0, 0.03);
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
  color: #333;
}

.duration-value:hover {
  background-color: rgba(63, 81, 181, 0.1);
}

.timing-operator {
  color: #666;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.timing-timestamp {
  color: #666;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.edited-badge {
  display: inline-block;
  color: #ff9800;
  font-size: 10px;
  padding: 2px 6px;
  background: #fff3e0;
  border-radius: 4px;
  font-weight: 500;
  text-transform: uppercase;
}

.server-badge {
  display: inline-block;
  color: #2196f3;
  font-size: 10px;
  padding: 2px 6px;
  background: #e3f2fd;
  border-radius: 4px;
  font-weight: 500;
  text-transform: uppercase;
}

.timing-description {
  font-size: 13px;
  color: #555;
  font-style: italic;
  background-color: rgba(0, 0, 0, 0.02);
  padding: 10px 12px;
  border-radius: 8px;
  border-left: 3px solid #3f51b5;
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.edit-time-field {
  width: 140px;
  margin: 0;
}

.edit-time-field ::ng-deep .mat-form-field-infix {
  width: 140px;
  padding: 0.5em 0;
}

.edit-time-field ::ng-deep input {
  font-family: 'Roboto Mono', monospace;
  font-size: 14px;
}

.edit-date-field {
  width: 200px;
  margin: 0 4px;
}

.edit-date-field ::ng-deep .mat-form-field-infix {
  width: 200px;
  padding: 0.5em 0;
}

.edit-date-field ::ng-deep input {
  font-size: 14px;
}

.edit-description-field {
  width: 220px;
  margin: 0;
}

.edit-description-field ::ng-deep .mat-form-field-infix {
  width: 220px;
  padding: 0.5em 0;
}

.edit-description-field ::ng-deep input {
  font-size: 14px;
}

.edit-container .edit-description-field + .edit-buttons {
  position: absolute;
  right: -70px;
  top: 50%;
  transform: translateY(-50%);
}

.edit-timeperiod {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
  padding: 10px 0;
  position: relative;
  z-index: 10;
}

.edit-timeperiod .edit-buttons {
  position: absolute;
  right: -70px;
  top: 50%;
  transform: translateY(-50%);
}

.time-period-value, .description-value {
  cursor: pointer;
  text-decoration: none;
  padding: 4px 8px;
  border-radius: 4px;
  color: #333;
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.time-period-value:hover, .description-value:hover {
  background-color: rgba(63, 81, 181, 0.1);
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.editable-cell {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  position: relative;
}

.editable-cell:hover {
  background-color: rgba(63, 81, 181, 0.08);
}

.editable-cell:hover .edit-icon {
  opacity: 1;
}

.cell-value {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.edit-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
  line-height: 16px;
  opacity: 0;
  color: #666;
  margin-left: 8px;
  transition: opacity 0.2s ease;
}

.edit-container {
  display: flex;
  flex-direction: column;
  padding: 4px;
  background-color: #f5f5f5;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.2s ease-out;
  position: relative;
  z-index: 10;
}

.edit-field {
  width: 100%;
  margin: 0;
}

.description-field {
  min-width: 200px;
}

.edit-actions {
  display: flex;
  justify-content: flex-end;
  gap: 4px;
  margin-top: 4px;
}

.timeperiod-edit {
  width: 100%;
}

.timeperiod-fields {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Table Container */
.table-container {
  width: 100%;
  overflow-x: auto;
}

.status-badges {
  display: flex;
  gap: 4px;
  margin-top: 4px;
}

::ng-deep .mat-form-field-appearance-outline .mat-form-field-outline {
  color: rgba(0, 0, 0, 0.2);
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 4px;
}

::ng-deep .mat-form-field-appearance-outline.mat-focused .mat-form-field-outline {
  color: #3f51b5;
}

::ng-deep .mat-form-field-appearance-outline .mat-form-field-infix {
  padding: 0.5em 0;
  width: auto !important;
}

::ng-deep .mat-form-field-appearance-outline .mat-form-field-label {
  top: 1.5em;
  margin-top: -0.25em;
}

::ng-deep .mat-form-field-wrapper {
  padding-bottom: 0.8em;
}


