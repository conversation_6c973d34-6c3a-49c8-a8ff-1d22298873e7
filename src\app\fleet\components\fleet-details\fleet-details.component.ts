import {
  Component,
  OnInit,
  AfterViewInit,
  ViewChild,
  OnDestroy,
  Output,
  EventEmitter,
  ViewEncapsulation
} from '@angular/core';
import {of, Subject} from 'rxjs';
import {takeUntil, catchError, finalize} from 'rxjs/operators';
import {Router, ActivatedRoute} from '@angular/router';
import {KeycloakService} from 'keycloak-angular';
import {MatPaginator} from '@angular/material/paginator';
import {MatDialog} from '@angular/material/dialog';
import {FormControl} from '@angular/forms';
import {MatSnackBar} from '@angular/material/snack-bar';

import {NotificationsService} from '@shared/services/notifications.service';
import {FleetService} from '@app/services/fleet/fleet.service';
import {Glider, GliderStatus, UpdateStatusPayload} from '@core/interfaces/glider.interace';
import {SharedVarService} from '@app/services/SharedVarService/shared-var-service.service';
import {statusFleets} from '../../enum/enums';
import {GliderMaintenance} from '@app/core/interfaces/gliderMaintenance.interface';
import {GliderMaintenanceService} from '@services/gliderMaintenance/gliderMaintenance.service';
import {FlightReviewService} from '@services/flight-review/flightReview.srvice';
import {
  RequestMaintenanceDialogComponent
} from '@app/fleet/components/fleet-details/maintenance-dialog/request-maintenance-dialog.component';

import {SoftwareVersionService} from '@services/software-versions/software-version.service';
import {SoftwareVersion} from '@core/interfaces/softwareversion.interface';
import {ConfirmSoftwareVersionDialogComponent} from '@shared/components/dialog-update-sw-version/update-sw-versions-dialog.component';
import {ConfirmStatusUpdateDialogComponent} from '@shared/components/confirm-status-update-dialog/confirm-status-update-dialog';
import {GliderStatusLogsService} from '@app/services/logs/glider-status-logs.service';
import {GliderStatusLog} from '@app/core/interfaces/glider-status-log.interface';

@Component({
  selector: 'app-fleet-details',
  templateUrl: './fleet-details.component.html',
  styleUrls: ['./fleet-details.component.css'],
  encapsulation: ViewEncapsulation.None,
})
export class FleetDetailsComponent implements OnInit, OnDestroy, AfterViewInit {

  private subject = new Subject<void>();

  dataGlider: Glider | null = null;

  statusFleets = statusFleets;
  maintenanceHistory: GliderMaintenance[] = [];
  isLoadingMaintenance = false;
  isLoadingIncidents = false;
  isLoadingLastFlights = false;
  isLoadingNextFlights = false;
  isLoadingStatusLogs = false;
  totalFlights = 0;
  lastIncidents: any[] = [];
  lastFlights: any[] = [];
  nextFlights: any[] = [];
  statusLogs: GliderStatusLog[] = [];

  showJetsonWarning = false;
  showAutopilotWarning = false;
  showFtsPixhawkWarning = false;
  showFtsRaspiWarning = false;

  availableStatuses: GliderStatus[] = [];
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @Output() eventData = new EventEmitter<boolean>();

  allStatuses: string[] = [
    this.statusFleets.ready,
    this.statusFleets.grounded,
    this.statusFleets.unavailable,
    this.statusFleets.maintenanceDue,
    this.statusFleets.postMaintenanceChecks,
    this.statusFleets.retired
  ];

  statusSequence: string[] = [
    this.statusFleets.ready,
    this.statusFleets.grounded,
    this.statusFleets.unavailable,
    this.statusFleets.maintenanceDue,
    this.statusFleets.postMaintenanceChecks,
    this.statusFleets.retired
  ];

  public updatingStatus = false;
  public loggedInUserEmail: string | undefined = '';
  public isReady = false;
  public isUpdatingToggle = false;
  allJetsonVersions: SoftwareVersion[] = [];
  filteredJetsonList: SoftwareVersion[] = [];
  jetsonVersionControl = new FormControl('');
  allAutopilotVersions: SoftwareVersion[] = [];
  filteredAutopilotList: SoftwareVersion[] = [];
  autopilotVersionControl = new FormControl('');
  allFtsPixhawkVersions: SoftwareVersion[] = [];
  filteredFtsPixhawkList: SoftwareVersion[] = [];
  allFtsRaspiVersions: SoftwareVersion[] = [];
  filteredFtsRaspiList: SoftwareVersion[] = [];

  constructor(
    private sharedVarService: SharedVarService,
    private routeActive: ActivatedRoute,
    private fleetService: FleetService,
    public dialog: MatDialog,
    private notificationsAlerts: NotificationsService,
    private gliderMaintenanceService: GliderMaintenanceService,
    private flightReviewService: FlightReviewService,
    private snackBar: MatSnackBar,
    private softwareUpdateService: SoftwareVersionService,
    private router: Router,
    private keycloakService: KeycloakService,
    private gliderStatusLogsService: GliderStatusLogsService,
  ) {
  }

  ngOnInit(): void {
    this.keycloakService.loadUserProfile().then(profile => {
      this.loggedInUserEmail = profile.email;
    }).catch(err => {
      console.error('Failed to load user profile', err);
    });
    this.sharedVarService.setValue('Glider details');
    this.fleetService.getFleetStatusList()
      .pipe(
        takeUntil(this.subject),
        catchError(err => {
          this.notificationsAlerts.openSnackBar('Error loading statuses', 'Ok');
          return of(null);
        })
      )
      .subscribe((res: any) => {
        if (res?.data) {
          this.availableStatuses = res.data as GliderStatus[];
        }
      });
    const fleetId = parseInt(this.routeActive.snapshot.params.id, 10);
    this.loadData(fleetId);
    this.softwareUpdateService.getSoftwareVersions()
      .pipe(
        takeUntil(this.subject),
        catchError(err => {
          this.notificationsAlerts.openSnackBar('Error loading software versions', 'Ok');
          return of([]);
        })
      )
      .subscribe();
  }

  ngAfterViewInit(): void {
  }

  ngOnDestroy(): void {
    this.subject.next();
    this.subject.complete();
  }

  loadData(fleetId: number): void {
    this.fleetService.getFleetById(fleetId)
      .pipe(
        takeUntil(this.subject),
        catchError(err => {
          this.notificationsAlerts.openSnackBar('Error loading fleet data', 'Ok');
          return of(null);
        })
      )
      .subscribe((res: any) => {
        if (!res) {
          return;
        }
        this.dataGlider = res as Glider;
        if (this.dataGlider) {
          this.showJetsonWarning = this.isVersionMismatch(
            this.dataGlider.jetsonSoftwareVersion,
            this.dataGlider.desiredJetsonSoftwareVersion
          );
          this.showAutopilotWarning = this.isVersionMismatch(
            this.dataGlider.autopilotSoftwareVersion,
            this.dataGlider.desiredAutopilotSoftwareVersion
          );
          this.showFtsPixhawkWarning = this.isVersionMismatch(
            this.dataGlider.ftsPixhawkSoftwareVersion,
            this.dataGlider.desiredFtsPixhawkSoftwareVersion
          );
          this.showFtsRaspiWarning = this.isVersionMismatch(
            this.dataGlider.ftsRaspiSoftwareVersion,
            this.dataGlider.desiredFtsRaspiSoftwareVersion
          );
        }
        this.dataGlider.gliderStatus = this.dataGlider.gliderStatus || this.getDefaultStatus();
        this.isReady = this.dataGlider.gliderStatus.name?.toLowerCase() === 'ready';

        if (!this.dataGlider.id) {
          return;
        }
        this.isLoadingMaintenance = true;
        this.gliderMaintenanceService.getMaintenancesByGliderId(this.dataGlider.id)
          .pipe(
            catchError(err => {
              this.notificationsAlerts.openSnackBar('Error loading maintenance history', 'Ok');
              return of<GliderMaintenance[]>([]);
            }),
            finalize(() => {
              this.isLoadingMaintenance = false;
            })
          )
          .subscribe(maintenances => {
            this.maintenanceHistory = maintenances
              .sort((a, b) => new Date(b.dueDate).getTime() - new Date(a.dueDate).getTime())
              .slice(0, 5);
          });
        if (this.dataGlider.name) {
          this.loadFlightData(this.dataGlider.name);
          this.loadStatusLogs(this.dataGlider.name);
        }
        this.initAutopilotVersions();
        this.initJetsonVersions();
        this.initFtsPixhawkVersions();
        this.initFtsRaspiVersions();
      });
  }

  loadFlightData(gliderName: string): void {
    this.isLoadingIncidents = true;
    this.flightReviewService.getFlightsByGlider(gliderName)
      .pipe(
        takeUntil(this.subject),
        catchError(err => {
          this.notificationsAlerts.openSnackBar('Error fetching incidents', 'Ok');
          return of(null);
        }),
        finalize(() => {
          this.isLoadingIncidents = false;
        })
      )
      .subscribe((res: any) => {
        if (!res?.data) {
          return;
        }
        const allFlights = res.data;
        const flightsWithIncidents = allFlights
          .filter((f: any) => f.incident != null)
          .sort((a: any, b: any) => Number(b.start_time) - Number(a.start_time));
        this.lastIncidents = flightsWithIncidents.slice(0, 5);
      });

    this.isLoadingLastFlights = true;
    this.flightReviewService.getLastFlightsByGlider(gliderName, 5)
      .pipe(
        takeUntil(this.subject),
        catchError(err => {
          this.notificationsAlerts.openSnackBar('Error fetching last flights', 'Ok');
          return of(null);
        }),
        finalize(() => {
          this.isLoadingLastFlights = false;
        })
      )
      .subscribe((res: any) => {
        this.lastFlights = res?.data || [];
      });

    this.isLoadingNextFlights = true;
    this.flightReviewService.getNextFlightsByGlider(gliderName, 5)
      .pipe(
        takeUntil(this.subject),
        catchError(err => {
          this.notificationsAlerts.openSnackBar('Error fetching next flights', 'Ok');
          return of(null);
        }),
        finalize(() => {
          this.isLoadingNextFlights = false;
        })
      )
      .subscribe((res: any) => {
        this.nextFlights = res?.data || [];
      });

    this.flightReviewService.getLastIncidents()
      .pipe(
        takeUntil(this.subject),
        catchError(err => {
          this.notificationsAlerts.openSnackBar('Error fetching last incidents', 'Ok');
          return of(null);
        }),
        finalize(() => {
          this.isLoadingIncidents = false;
        })
      )
      .subscribe((response: any) => {
        this.lastIncidents = response?.data || [];
      });
  }

  loadStatusLogs(gliderName: string): void {
    this.isLoadingStatusLogs = true;
    this.gliderStatusLogsService.getGliderStatusLogs(gliderName)
      .pipe(
        takeUntil(this.subject),
        catchError(err => {
          this.notificationsAlerts.openSnackBar('Error fetching status logs', 'Ok');
          return of([]);
        }),
        finalize(() => {
          this.isLoadingStatusLogs = false;
        })
      )
      .subscribe((logs: GliderStatusLog[]) => {
        const filteredLogs = logs
          .filter(log => log.oldGliderStatus?.id !== log.newGliderStatus?.id)
          .sort((a, b) => {
            return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
          });

        this.statusLogs = filteredLogs.slice(0, 5);
      });
  }

  initAutopilotVersions(): void {
    this.softwareUpdateService.getSoftwareVersionsByTypeId(1)
      .pipe(takeUntil(this.subject))
      .subscribe(vers => {
        this.allAutopilotVersions = vers;
        this.filteredAutopilotList = vers;
      });
  }

  initJetsonVersions(): void {
    this.softwareUpdateService.getSoftwareVersionsByTypeId(2)
      .pipe(takeUntil(this.subject))
      .subscribe(vers => {
        this.allJetsonVersions = vers;
        this.filteredJetsonList = vers;
      });
  }

  initFtsPixhawkVersions(): void {
    this.softwareUpdateService.getSoftwareVersionsByTypeId(5)
      .pipe(takeUntil(this.subject))
      .subscribe(vers => {
        this.allFtsPixhawkVersions = vers;
        this.filteredFtsPixhawkList = vers;
      });
  }

  initFtsRaspiVersions(): void {
    this.softwareUpdateService.getSoftwareVersionsByTypeId(4)
      .pipe(takeUntil(this.subject))
      .subscribe(vers => {
        this.allFtsRaspiVersions = vers;
        this.filteredFtsRaspiList = vers;
      });
  }

  private isVersionMismatch(current?: SoftwareVersion, desired?: SoftwareVersion): boolean {
    if (!current || !desired) {
      return false;
    }
    return current.id !== desired.id;
  }

  searchAutopilot(value: string): void {
    const val = (value || '').toLowerCase();
    this.filteredAutopilotList = this.allAutopilotVersions.filter(
      v => v.name.toLowerCase().includes(val)
    );
  }

  selectAutopilot(version: SoftwareVersion): void {
    if (!this.dataGlider) {
      return;
    }

    const oldVer = this.dataGlider.desiredAutopilotSoftwareVersion?.name || 'none';
    const dialogRef = this.dialog.open(ConfirmSoftwareVersionDialogComponent, {
      width: '400px',
      data: {oldVersion: oldVer, newVersion: version.name}
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result === true) {
        this.updateGliderSoftware('desiredAutopilotSoftwareVersionId', version.id);
      }
    });
  }

  searchJetson(value: string): void {
    const val = (value || '').toLowerCase();
    this.filteredJetsonList = this.allJetsonVersions.filter(
      v => v.name.toLowerCase().includes(val)
    );
  }

  selectJetson(version: SoftwareVersion): void {
    if (!this.dataGlider) {
      return;
    }

    const oldVer = this.dataGlider.desiredJetsonSoftwareVersion?.name || 'none';
    const dialogRef = this.dialog.open(ConfirmSoftwareVersionDialogComponent, {
      width: '400px',
      data: {oldVersion: oldVer, newVersion: version.name}
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result === true) {
        this.updateGliderSoftware('desiredJetsonSoftwareVersionId', version.id);
      }
    });
  }

  searchFtsPixhawk(value: string): void {
    const val = (value || '').toLowerCase();
    this.filteredFtsPixhawkList = this.allFtsPixhawkVersions.filter(
      v => v.name.toLowerCase().includes(val)
    );
  }

  selectFtsPixhawk(version: SoftwareVersion): void {
    if (!this.dataGlider) {
      return;
    }

    const oldVer = this.dataGlider.desiredFtsPixhawkSoftwareVersion?.name || 'none';
    const dialogRef = this.dialog.open(ConfirmSoftwareVersionDialogComponent, {
      width: '400px',
      data: {oldVersion: oldVer, newVersion: version.name}
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result === true) {
        this.updateGliderSoftware('desiredFtsPixhawkSoftwareVersionId', version.id);
      }
    });
  }

  searchFtsRaspi(value: string): void {
    const val = (value || '').toLowerCase();
    this.filteredFtsRaspiList = this.allFtsRaspiVersions.filter(
      v => v.name.toLowerCase().includes(val)
    );
  }

  selectFtsRaspi(version: SoftwareVersion): void {
    if (!this.dataGlider) {
      return;
    }

    const oldVer = this.dataGlider.desiredFtsRaspiSoftwareVersion?.name || 'none';
    const dialogRef = this.dialog.open(ConfirmSoftwareVersionDialogComponent, {
      width: '400px',
      data: {oldVersion: oldVer, newVersion: version.name}
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result === true) {
        this.updateGliderSoftware('desiredFtsRaspiSoftwareVersionId', version.id);
      }
    });
  }

  updateGliderSoftware(fieldName: string, versionId: number | string): void {
    if (!this.dataGlider) {
      return;
    }

    const payload = {[fieldName]: versionId};
    this.fleetService.updateGlider(this.dataGlider.id, payload)
      .pipe(
        takeUntil(this.subject),
        catchError(err => {
          this.notificationsAlerts.openSnackBar('Error updating software version', 'OK');
          return of(null);
        })
      )
      .subscribe((updatedGlider: Glider | null) => {
        if (!updatedGlider) {
          return;
        }

        this.dataGlider = updatedGlider;

        if (fieldName === 'desiredAutopilotSoftwareVersionId') {
          const name = updatedGlider.desiredAutopilotSoftwareVersion?.name || '';
          this.autopilotVersionControl.setValue(name);
        }
        if (fieldName === 'desiredJetsonSoftwareVersionId') {
          const name = updatedGlider.desiredJetsonSoftwareVersion?.name || '';
          this.jetsonVersionControl.setValue(name);
        }
        this.snackBar.open('Desired software version updated successfully!', 'Close', {
          duration: 3000
        });
        this.showJetsonWarning = this.isVersionMismatch(
          this.dataGlider.jetsonSoftwareVersion,
          this.dataGlider.desiredJetsonSoftwareVersion
        );
        this.showAutopilotWarning = this.isVersionMismatch(
          this.dataGlider.autopilotSoftwareVersion,
          this.dataGlider.desiredAutopilotSoftwareVersion
        );
        this.showFtsPixhawkWarning = this.isVersionMismatch(
          this.dataGlider.ftsPixhawkSoftwareVersion,
          this.dataGlider.desiredFtsPixhawkSoftwareVersion
        );
        this.showFtsRaspiWarning = this.isVersionMismatch(
          this.dataGlider.ftsRaspiSoftwareVersion,
          this.dataGlider.desiredFtsRaspiSoftwareVersion
        );
      });
  }

  openRequestMaintenanceDialog(): void {
    if (!this.dataGlider) {
      return;
    }

    const dialogRef = this.dialog.open(RequestMaintenanceDialogComponent, {
      width: '500px',
      data: {gliderName: this.dataGlider.name, userEmail: this.loggedInUserEmail}
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.sendUnscheduledMaintenanceRequest(result);
      }
    });
  }

  sendUnscheduledMaintenanceRequest(payload: any): void {
    this.gliderMaintenanceService.requestUnscheduledMaintenance(payload)
      .pipe(
        catchError((err) => {
          this.notificationsAlerts.openSnackBar('Error requesting maintenance', 'Ok');
          throw err;
        })
      )
      .subscribe({
        next: (response) => {
          if (response && response.message) {
            this.notificationsAlerts.openSnackBar(
              'Unscheduled maintenance notification sent successfully',
              'Ok'
            );
          }
        },
        error: (error) => {
          console.error('Error:', error);
          this.notificationsAlerts.openSnackBar(
            'Error requesting maintenance',
            'Ok'
          );
        }
      });
  }

  updateStatus(newStatus: string): void {
    if (!this.dataGlider || this.updatingStatus) {
      return;
    }

    const dialogRef = this.dialog.open(ConfirmStatusUpdateDialogComponent, {
      width: '400px',
      data: {newStatus}
    });

    dialogRef.afterClosed().subscribe(note => {
      if (note) {
        this.updatingStatus = true;
        this.snackBar.open(`Updating status to "${newStatus}"...`, 'Close', {duration: 3000});

        const payload: UpdateStatusPayload & { note: string; userEmail: string } = {
          gliderName: this.dataGlider!.name,
          status: this.formatStatusForApi(newStatus),
          note,
          userEmail: this.loggedInUserEmail || ''
        };

        this.fleetService.updateDroneStatus(payload)
          .pipe(
            catchError(err => {
              this.notificationsAlerts.openSnackBar('Failed to update status', 'Close', 3000);
              this.dataGlider!.gliderStatus = this.getDefaultStatus();
              return of(null);
            }),
            finalize(() => {
              this.updatingStatus = false;
            })
          )
          .subscribe((res: Glider | null) => {
            if (res) {
              this.dataGlider = res;
              this.snackBar.open('Status updated successfully', 'Close', {duration: 3000});
              this.isReady = this.dataGlider.gliderStatus.name.toLowerCase() === 'ready';
            }
          });
      }
    });
  }
  public copyToClipboard(text: string) {
    if (!text) {
      return;
    }
    navigator.clipboard.writeText(text).then(() => {
      this.notificationsAlerts.openSnackBar('Copied to clipboard', 'Close', 3000);
    }).catch(err => {
      console.error('Failed to copy', err);
    });
  }


  public formatStatusForApi(status: string): string {
    return status.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z\-]/g, '');
  }

  public getDefaultStatus(): GliderStatus {
    const unavailableStatus = this.availableStatuses.find(
      s => s.name === this.statusFleets.unavailable
    );
    if (unavailableStatus) {
      return unavailableStatus;
    } else {
      return {
        id: 0,
        name: this.statusFleets.unavailable,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        colorHexcode: "CCCCCC"
      };
    }
  }

  public getStatusClass(statusName: string): string {
    switch ((statusName || '').toLowerCase()) {
      case 'ready':
        return 'btn-ready';
      case 'grounded':
        return 'btn-grounded';
      case 'unavailable':
        return 'btn-unavailable';
      case 'maintenance due':
        return 'btn-maintenance-due';
      case 'post-maintenance-checks':
        return 'btn-post-maintenance-checks';
      case 'retired':
        return 'btn-retired';
      default:
        return 'btn-unavailable';
    }
    return '808080';
  }


  public goToCreateVersion(type: 'jetson' | 'autopilot' | 'pixhawk' | 'ftsPixhawk' | 'ftsRaspi'): void {
    alert('functionality in progress');
    // this.router.navigate(['/software/create'], { queryParams: { type } });
  }

  getLastStatusChangeTooltip(): string {
    if (!this.statusLogs || this.statusLogs.length === 0) {
      return 'No status change history available';
    }
    
    const lastChange = this.statusLogs[0];
    return `Status changed from "${lastChange.oldGliderStatus?.name || 'N/A'}" to "${lastChange.newGliderStatus?.name || 'N/A'}"
           by ${lastChange.userEmail}
           on ${new Date(lastChange.createdAt).toLocaleDateString()}
           
           Reason: ${lastChange.note || 'No reason provided'}`;
  }
  
  scrollToStatusHistory(): void {
    const element = document.getElementById('statusHistorySection');
    if (element) {
      element.classList.add('highlight');
      
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
      
      setTimeout(() => {
        element.classList.remove('highlight');
      }, 2000);
    }
  }
  
  isCurrentStatus(statusId?: number): boolean {
    if (!statusId || !this.dataGlider?.gliderStatus?.id) {
      return false;
    }
    return statusId === this.dataGlider.gliderStatus.id;
  }
}
