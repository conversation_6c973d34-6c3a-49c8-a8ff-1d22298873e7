.layout-container {
  width: 100%;
  position: relative;
  display: flex;
}

.aside {
  width: 250px;
  background-color: white;
  height: 100vh;
  border: none;
  transition: width 0.3s ease;
  flex-shrink: 0;
  position: relative;
  z-index: 10;
  border-right: 1px solid #e0e0e0;
}

.aside.collapsed {
  width: 70px;
}



.container {
  flex: 1;
  transition: all 0.3s ease;
  min-width: 0;
}

.topbar {
  border: none;
  padding-top: 25px;
  padding-bottom: 25px;
  padding-left: 15px;
  padding-right: 15px;
  margin-left: 0;
  font-weight: bold;
  width: 100%;
  font-size: 18px;
}

.content {
  vertical-align: top;
  border: none;
  width: 100%;
  margin-left: 15px;
  overflow: auto;
  background-color: white;
}

@media (max-width: 768px) {
  .layout-container {
    flex-direction: column;
  }

  .aside {
    width: 100% !important;
    height: auto;
    order: 2;
  }

  .aside.collapsed {
    width: 100% !important;
  }

  .sidebar-toggle-btn {
    display: none;
  }

  .container {
    order: 1;
    width: 100% !important;
  }

  .sidebar-container {
    width: 100% !important;
  }

  .sidebar-container.collapsed {
    width: 100% !important;
  }

  .navigate {
    display: flex;
    flex-wrap: wrap;
    height: auto !important;
  }

  .navigate-item,
  .navigate-item-active {
    width: auto !important;
    margin: 5px !important;
    flex: 1;
    min-width: 120px;
  }
}

@media (max-width: 480px) {
  .navigate-item,
  .navigate-item-active {
    width: 100% !important;
    margin: 5px 10px !important;
  }
}
