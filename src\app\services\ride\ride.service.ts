import {HttpClient, HttpParams, HttpHeaders} from '@angular/common/http';
import {Injectable} from '@angular/core';
import {Response} from '@app/core/interfaces/responses/response.interface';
import {Ride, RideExportFilters, RideStatus} from '@app/core/interfaces/ride.interface';
import {typeAction} from '@app/shared/utils/enum';
import {environment} from '@src/environments/environment';
import {KeycloakService} from 'keycloak-angular';
import {Observable, Subject} from 'rxjs';
import {from} from 'rxjs';
import {switchMap} from 'rxjs/operators';

@Injectable()
export class RideService {
  private apiUrl = environment.urlMsRides;
  private subjectRides = new Subject<Ride[]>();
  public action: typeAction | undefined;

  constructor(private http: HttpClient, private keycloakService: KeycloakService) {
  }

  getRideList(
    skip: number = 0,
    limit: number = 5000,
    operator_id?: string,
    ride_status_id?: number,
    glider_name?: string,
    route_id?: number,
    start_time?: string,
    end_time?: string,
    customer_id?: number
  ): Observable<Response<Ride[]> | Ride[]> {
    return from(this.keycloakService.getToken()).pipe(
      switchMap((token) => {
        const headers = new HttpHeaders({
          Authorization: `Bearer ${token}`,
        });

        let params = new HttpParams()
          .set('limit', limit.toString())
          .set('skip', skip.toString());

        if (operator_id) {
          params = params.set('operator_id', operator_id);
        }

        if (ride_status_id) {
          params = params.set('ride_status_id', ride_status_id.toString());
        }

        if (glider_name) {
          params = params.set('glider_name', glider_name);
        }

        if (route_id) {
          params = params.set('route_id', route_id.toString());
        }

        if (start_time) {
          try {
            const date = new Date(start_time);
            if (!isNaN(date.getTime())) {
              params = params.set('start_time', start_time);
            }
          } catch (e) {
          }
        }

        if (end_time) {
          try {
            const date = new Date(end_time);
            if (!isNaN(date.getTime())) {
              params = params.set('end_time', end_time);
            }
          } catch (e) {
          }
        }

        if (customer_id) {
          params = params.set('customer_id', customer_id.toString());
        }

        return this.http.get<Response<Ride[]> | Ride[]>(`${this.apiUrl}/rides`, {headers, params});
      })
    );
  }

  getRideCSV(params: RideExportFilters): Observable<any> {
    let url_params = new HttpParams();

    if (params.include_package && !params.include_no_package) {
      url_params = url_params.append('ride[has_package]', 'true');
    }
    if (!params.include_package && params.include_no_package) {
      url_params = url_params.append('ride[has_package]', 'false');
    }


    if (params.start_date) {
      url_params = url_params.append('ride[start_date]', params.start_date);
    }

    if (params.end_date) {
      url_params = url_params.append('ride[end_date]', params.end_date);
    }

    if (params.glider) {
      url_params = url_params.append('ride[glider_id]', params.glider.id);
    }

    if (params.start_location) {
      url_params = url_params.append('ride[from_location_id]', params.start_location.id);
    }

    if (params.destination_location) {
      url_params = url_params.append('ride[to_location_id]', params.destination_location.id);
    }

    return this.http.get(`${this.apiUrl}.csv`, {responseType: 'text', params: url_params});
  }

  exportRidesCSV(
    operator_id?: string,
    ride_status_id?: number,
    glider_name?: string,
    route_id?: number,
    start_time?: string,
    end_time?: string,
    customer_id?: number
  ): Observable<any> {
    return from(this.keycloakService.getToken()).pipe(
      switchMap((token) => {
        const headers = new HttpHeaders({
          Authorization: `Bearer ${token}`,
        });

        let params = new HttpParams();

        if (operator_id) {
          params = params.set('operator_id', operator_id);
        }

        if (ride_status_id) {
          params = params.set('ride_status_id', ride_status_id.toString());
        }

        if (glider_name) {
          params = params.set('glider_name', glider_name);
        }

        if (route_id) {
          params = params.set('route_id', route_id.toString());
        }

        if (start_time) {
          try {
            const date = new Date(start_time);
            if (!isNaN(date.getTime())) {
              params = params.set('start_time', start_time);
            }
          } catch (e) {
          }
        }

        if (end_time) {
          try {
            const date = new Date(end_time);
            if (!isNaN(date.getTime())) {
              params = params.set('end_time', end_time);
            }
          } catch (e) {
          }
        }

        if (customer_id) {
          params = params.set('customer_id', customer_id.toString());
        }

        return this.http.get(`${this.apiUrl}/csv-export/rides`, {headers, params, responseType: 'text'});
      })
    );
  }

  getRidesStatusList(): Observable<Response<RideStatus[]>> {
    return this.http.get<Response<RideStatus[]>>(`${this.apiUrl}/ride-statuses`);
  }

  updateRide(rideId: number, ride: any): Observable<Response<Ride>> {
    return from(this.keycloakService.getToken()).pipe(
      switchMap((token) => {
        const headers = new HttpHeaders({
          Authorization: `Bearer ${token}`,
        });
        return this.http.patch<Response<Ride>>(
          `${this.apiUrl}/rides/${rideId}`,
          ride,
          { headers }
        );
      })
    );
  }

  deleteRide(rideId: number): Observable<Response<any>> {
    return from(this.keycloakService.getToken()).pipe(
      switchMap((token) => {
        const headers = new HttpHeaders({
          Authorization: `Bearer ${token}`,
        });
        return this.http.delete<Response<any>>(
          `${this.apiUrl}/rides/${rideId}`,
          { headers }
        );
      })
    );
  }

  notifyDataSubject(ride: Ride[], action?: typeAction): void {
    this.action = action;
    this.subjectRides.next(ride);
  }

  listenDataSubjectRide(): Observable<Ride[]> {
    return this.subjectRides.asObservable();
  }
}
