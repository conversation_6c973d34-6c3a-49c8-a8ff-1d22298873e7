.status-chip {
  .mat-icon {
    width: 16px;
    height: 16px;
    font-size: 16px;
  }

  .mat-icon-button .mat-icon {
    line-height: 16px;
  }

  .mat-button-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
  }


  .mat-standard-chip {
    border-radius: 8px;
  }


  .status-action-icon {
    transition: background-color 0.2s ease, opacity 0.2s ease;
  }

  .status-action-icon:hover {
    background-color: rgba(0, 0, 0, 0.08);
  }


  .status-action-icon:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  .status-container {
    display: flex;

  }
}

.status-chip {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 8px;
  padding: 0 8px;
  background-color: #f5f5f5;
  font-size: 14px;
  line-height: 32px;
  height: 32px;
  min-width: 110px;
}

.btn-gray {
  background-color: #e0e0e0;
  color: #424242;
}

.btn-yellow {
  background-color: #fff9c4;
  color: #795548;
}

.btn-green {
  background-color: #c8e6c9;
  color: #1b5e20;
}

.btn-red {
  background-color: #ffcdd2;
  color: #b71c1c;
}

.status-text {
  margin-right: 4px;
  user-select: none;
  white-space: nowrap;
  display: inline-block;
  text-align: left;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.status-action-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.status-filled {
  border-radius: 16px;
  padding: 6px 10px 6px 12px;
  min-width: 140px;
  min-height: 36px;
  color: white;
  cursor: pointer;
  transition: background-color 0.3s ease;
  max-width: 180px;
  width: auto;
}

.status-tooltip {
  font-size: 12px;
  white-space: pre-line !important;
  max-width: 300px !important;
  background-color: rgba(33, 33, 33, 0.95) !important;
  padding: 8px 12px !important;
  border-radius: 4px !important;
  color: white !important;
  line-height: 1.5 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
}

.menu-group-title {
  font-size: 13px;
  color: rgba(0, 0, 0, 0.7);
  font-weight: 500;
}

.table-container {
  overflow-x: auto;
  width: 100%;
  position: relative;
  max-width: 100%;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.table-container table {
  min-width: 1700px;
  width: 100%;
}

.table-container::-webkit-scrollbar {
  height: 8px;
}

.table-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.4);
}

table.mat-table {
  width: 100%;
  min-width: 1700px;
  table-layout: auto;
  border-collapse: separate;
  border-spacing: 0;
  border: none;
}



table.mat-table .mat-column-time_departure {
  width: 140px !important;
  max-width: 140px !important;
  padding: 8px 12px !important;
}

table.mat-table .mat-column-date_departure {
  width: 140px !important;
  max-width: 140px !important;
  padding: 8px 12px !important;
}

table.mat-table .mat-column-location_departure {
  min-width: 160px !important;
  width: 160px !important;
  max-width: 160px !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 8px 10px !important;
}

table.mat-table .mat-column-location_arrival {
  min-width: 160px !important;
  width: 160px !important;
  max-width: 160px !important;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 8px 10px !important;
}

table.mat-table .mat-column-glider_name {
  width: 130px !important;
  padding: 4px !important;
}

table.mat-table .mat-column-operator_email {
  min-width: 130px !important;
  width: 130px !important;
  padding: 8px 10px !important;
}

table.mat-table .mat-column-status {
  min-width: 180px !important;
  width: 180px !important;
  padding: 8px 10px !important;
}

table.mat-table .mat-column-actions {
  min-width: 90px !important;
  width: 90px !important;
  padding: 8px 8px !important;
}

table.mat-table .mat-cell,
table.mat-table .mat-header-cell {
  padding: 8px 8px !important;
  white-space: nowrap;
  overflow: hidden;
}

.mat-row:nth-child(even) {
  background-color: #f5f5f5;
}

.mat-row:nth-child(odd) {
  background-color: #ffffff;
}

.mat-row {
  height: 56px;
  border-bottom: 1px solid #e0e0e0;
}


.filters-container {
  margin-bottom: 20px;
}

.filters-row {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
  min-height: 72px;
}

.filters-row:nth-child(2) {
  align-items: stretch;
}

.filters-row:nth-child(2) .filter-item {
  display: flex;
  align-items: stretch;
}

.filter-item {
  flex: 1;
  min-width: 180px;
  max-width: 250px;
}

.search-filter {
  flex: 2;
  max-width: 350px;
}

.filter-actions {
  display: flex;
  gap: 8px;
  margin-left: auto;
  align-items: center;
}

.btn-style {
  height: 36px;
  display: flex;
  align-items: center;
  gap: 4px;
  border-radius: 4px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 0 16px;
}

.clear-button {
  background-color: #c8e6c9;
  color: #1b5e20;
}

.export-button {
  background-color: #bbdefb;
  color: #0d47a1;
}

.mat-form-field {
  width: 100%;
  min-height: 72px;
  height: 72px;
}

.mat-form-field-appearance-outline .mat-form-field-wrapper {
  margin: 0;
  padding-bottom: 1.25em;
  height: 100%;
}

.mat-form-field-appearance-outline .mat-form-field-flex {
  align-items: center;
  min-height: 56px;
}

.mat-form-field-appearance-outline .mat-form-field-infix {
  padding: 1em 0 1em 0;
  border-top: 0.84375em solid transparent;
}

.mat-form-field-appearance-outline .mat-form-field-label {
  top: 1.84375em;
  margin-top: -0.25em;
}

.mat-form-field-appearance-outline .mat-form-field-outline {
  color: rgba(0, 0, 0, 0.12);
}

.mat-form-field-appearance-outline .mat-form-field-outline-thick {
  color: rgba(0, 0, 0, 0.38);
}

.mat-form-field-appearance-outline.mat-focused .mat-form-field-outline-thick {
  color: #3f51b5;
}

.mat-select-panel {
  max-height: 300px !important;
}

.mat-select-panel mat-option {
  height: 36px;
  line-height: 36px;
}

.operator-search-container {
  padding: 8px;
  position: sticky;
  top: 0;
  z-index: 1;
  background-color: white;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

.operator-search-input-container {
  display: flex;
  align-items: center;
  border: 1px solid rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  padding: 0 8px;
  height: 40px;
  background-color: #f5f5f5;
}

.operator-search-input {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  padding: 8px;
  font-size: 14px;
  width: 100%;
}

.operator-search-icon {
  color: rgba(0, 0, 0, 0.54);
  font-size: 20px;
  width: 20px;
  height: 20px;
}

.mat-select-trigger {
  display: flex;
  align-items: center;
  height: 100%;
}

.mat-select-value {
  max-width: 100%;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.mat-form-field-appearance-outline .mat-form-field-label-wrapper {
  top: -0.5em;
  padding-top: 0.8em;
}

.mat-form-field-appearance-outline.mat-form-field-can-float.mat-form-field-should-float .mat-form-field-label {
  transform: translateY(-1.5em) scale(0.75);
  width: 133.33333%;
}

.mat-form-field-appearance-outline .mat-form-field-label {
  top: 1.8em;
  margin-top: -0.25em;
  z-index: 2;
}

@media (max-width: 1200px) {
  .filter-item {
    max-width: 300px;
  }

  .search-filter {
    max-width: 400px;
  }

  .filters-row {
    min-height: 80px;
  }
}

@media (max-width: 768px) {
  .filter-item, .search-filter {
    min-width: 100%;
    max-width: 100%;
  }

  .filter-actions {
    width: 100%;
    justify-content: flex-end;
    margin-top: 8px;
  }

  .filters-row {
    min-height: 84px;
  }

  .mat-form-field {
    min-height: 76px;
  }
}

.header-cell {
  padding: 0 4px;
  height: 56px;
  vertical-align: middle;
  position: relative;
  text-align: center;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: nowrap;
  white-space: nowrap;
  min-height: 40px;
  gap: 2px;
}

.header-content span {
  margin-right: 0;
  font-weight: 500;
}

.sort-button {
  width: 20px !important;
  height: 20px !important;
  line-height: 20px !important;
  padding: 0 !important;
  margin-left: 0;
  min-width: 20px !important;
}

.sort-button .mat-button-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  line-height: 1;
}

.sort-icon {
  font-size: 14px;
  width: 14px;
  height: 14px;
  line-height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sort-icon .material-icons {
  font-size: 14px;
  width: 14px;
  height: 14px;
  line-height: 14px;
}

th.mat-header-cell {
  overflow: visible;
  vertical-align: middle;
  text-align: center;
  height: 56px;
  padding: 0 4px;
}

.headerTable th {
  font-weight: 500;
}

.mat-column-time_departure,
.mat-column-date_departure,
td.mat-cell.cdk-column-time_departure,
td.mat-cell.cdk-column-date_departure,
th.mat-header-cell.cdk-column-time_departure,
th.mat-header-cell.cdk-column-date_departure {
  min-width: 120px;
  max-width: 120px;
  width: 120px;
}

.mat-column-time_arrival,
td.mat-cell.cdk-column-time_arrival,
th.mat-header-cell.cdk-column-time_arrival {
  min-width: 120px;
  max-width: 120px;
  width: 120px;
}

.mat-column-glider,
.mat-column-operator,
td.mat-cell.cdk-column-glider,
td.mat-cell.cdk-column-operator {
  min-width: 130px;
  max-width: 130px;
  width: 130px;
}

.mat-column-location_departure,
.mat-column-location_arrival,
td.mat-cell.cdk-column-location_departure,
td.mat-cell.cdk-column-location_arrival,
th.mat-header-cell.cdk-column-location_departure,
th.mat-header-cell.cdk-column-location_arrival {
  min-width: 120px;
  max-width: 120px;
  width: 120px !important;
  padding-right: 10px;
}

.mat-column-status,
td.mat-cell.cdk-column-status,
th.mat-header-cell.cdk-column-status {
  width: 150px;
  padding-right: 8px;
}

.mat-column-reason,
td.mat-cell.cdk-column-reason,
th.mat-header-cell.cdk-column-reason {
  min-width: 180px;
  max-width: 180px;
  width: 180px;
  padding-left: 8px !important;
  padding-right: 8px;
}

.mat-column-timetracking,
td.mat-cell.cdk-column-timetracking,
th.mat-header-cell.cdk-column-timetracking {
  min-width: 210px;
  max-width: 210px;
  width: 210px;
  padding-right: 8px;
}

.mat-column-actions,
td.mat-cell.cdk-column-actions,
th.mat-header-cell.cdk-column-actions {
  min-width: 90px;
  max-width: 90px;
  width: 90px;
  padding-right: 8px;
}

.mat-cell {
  padding: 0 4px;
  white-space: nowrap;
  text-align: center;
  vertical-align: middle;
  height: 57px;
}

.mat-cell span {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}


.mat-column-location_departure span,
.mat-column-location_arrival span {
  max-width: 100%;
  width: 100%;
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}


.mat-column-location_departure .header-content,
.mat-column-location_arrival .header-content,
th.mat-header-cell.cdk-column-location_departure .header-content,
th.mat-header-cell.cdk-column-location_arrival .header-content {
  justify-content: center;
  padding: 0 4px;
}

.truncated-text {
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  cursor: pointer;
}

.skeleton-loader {
  animation: pulse 1.5s ease-in-out 0.5s infinite;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  border-radius: 4px;
}

.skeleton-row {
  height: 48px;
  display: flex;
  align-items: center;
  padding: 0 8px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
  min-width: 1400px;
}

.skeleton-cell {
  height: 16px;
  border-radius: 4px;
  margin: 0 5px;
}

.skeleton-cell-sm {
  width: 110px;
}

.timetracking-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
  width: 100%;
  padding: 6px;
  border-radius: 8px;
  transition: all 0.3s ease;
  background-color: rgba(0, 0, 0, 0.01);
  min-width: 190px;
}

.timetracking-container:hover {
  background-color: rgba(0, 0, 0, 0.03);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.timer-display {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.02);
  transition: background-color 0.3s ease;
}

.timer-running {
  background-color: rgba(76, 175, 80, 0.05);
}

.timer-status-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
  color: #757575;
}

.timer-running .timer-status-icon {
  color: #4CAF50;
}

.timer-value {
  font-family: 'Roboto Mono', monospace;
  font-size: 13px;
  font-weight: 400;
  white-space: nowrap;
  color: #333;
}

.timer-controls {
  display: flex;
  gap: 6px;
  justify-content: center;
  flex-wrap: nowrap;
}

.timer-button {
  width: 28px !important;
  height: 28px !important;
  padding: 0 !important;
  margin: 0 !important;
  transition: background-color 0.2s ease !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.timer-button:hover {
  background-color: rgba(0, 0, 0, 0.04) !important;
}

.timer-icon {
  font-size: 16px !important;
  width: 16px !important;
  height: 16px !important;
  line-height: 16px !important;
}

.stop-button {
  color: #f44336 !important;
}

.history-button {
  color: #7986cb !important;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}

.timing-history-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 99999;
  pointer-events: all;
}

.timing-history-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.timing-history-content {
  position: relative;
  z-index: 1001;
  max-width: 100%;
  max-height: 90vh;
  animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.delete-action {
  color: #f44336 !important;
}

.delete-text {
  color: #f44336 !important;
  margin-left: 8px;
}

.btn-detail {
  color: #3f51b5;
  cursor: pointer;
  text-decoration: none;
  transition: color 0.2s ease;
}

.btn-detail:hover {
  color: #1a237e;
  text-decoration: underline;
}

.warning-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
  margin-right: 4px;
  cursor: pointer;
}

.warning-icon-red {
  color: #f44336;
}

.warning-icon-yellow {
  color: #ffc107;
}

.mr-1 {
  margin-right: 4px;
}

.skeleton-cell-md {
  width: 130px;
}

.skeleton-cell-lg {
  width: 120px;
}

@keyframes pulse {
  0% {
    background-position: 100% 0;
  }
  100% {
    background-position: -100% 0;
  }
}

.filter-item {
  .mat-form-field {
    width: 100%;
    min-height: 72px;
    height: 72px;

    input[type="datetime-local"] {
      font-family: 'Roboto', sans-serif;
      font-size: 14px;
      color: #333;
      border-radius: 4px;
      height: 56px;
      line-height: 1.5;
      padding: 15px 11px;

      &::-webkit-calendar-picker-indicator {
        cursor: pointer;
        opacity: 0.7;
        filter: invert(0.5);
        margin-left: 4px;

        &:hover {
          opacity: 1;
          filter: invert(0.3);
        }
      }

      &:focus {
        outline: none;
      }

      &::-webkit-datetime-edit {
        padding: 0;
        line-height: 1.5;
      }

      &::-webkit-datetime-edit-fields-wrapper {
        padding: 0;
        line-height: 1.5;
      }

      &::-webkit-datetime-edit-text {
        color: #666;
        padding: 0 2px;
      }

      &::-webkit-datetime-edit-month-field,
      &::-webkit-datetime-edit-day-field,
      &::-webkit-datetime-edit-year-field,
      &::-webkit-datetime-edit-hour-field,
      &::-webkit-datetime-edit-minute-field {
        padding: 0 2px;
        color: #333;
        border-radius: 2px;

        &:focus {
          background-color: #e3f2fd;
          outline: none;
        }
      }
    }

    /* Clear button styling */
    button[matSuffix] {
      .mat-icon {
        font-size: 18px;
        width: 18px;
        height: 18px;
        color: #999;
      }

      &:hover .mat-icon {
        color: #f44336;
      }
    }
  }
}

.filters-row:nth-child(2) .filter-item .mat-form-field {
  height: 72px !important;

  .mat-form-field-wrapper {
    height: 100%;
    display: flex;
    align-items: center;
  }

  .mat-form-field-flex {
    height: 56px;
    align-items: center;
  }

  .mat-form-field-infix {
    display: flex;
    align-items: center;
    padding: 0;
    border-top: none;
  }

  input[type="datetime-local"] {
    height: 40px !important;
    padding: 8px 12px !important;
    margin: 0;
    display: flex;
    align-items: center;
  }
}

@media (max-width: 768px) {
  .filter-item {
    .mat-form-field {
      input[type="datetime-local"] {
        font-size: 16px; /* Prevent zoom on iOS */
      }
    }
  }

  .filters-row:nth-child(2) .filter-item .mat-form-field {
    height: 76px !important;

    input[type="datetime-local"] {
      height: 44px !important;
      font-size: 16px !important;
    }
  }
}
