import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DialogLocationInfoComponent } from './dialog-location-information.component';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { LocationService } from '@app/services/location/location.service';
import { AuthModule } from '@auth/auth.module';

describe('DialogLocationInfoComponent', () => {
  let component: DialogLocationInfoComponent;
  let fixture: ComponentFixture<DialogLocationInfoComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DialogLocationInfoComponent],
      providers: [
        {
          provide: MAT_DIALOG_DATA,
          useValue: {},
        },
        {
          provide: MatDialogRef,
          useValue: {},
        },
        LocationService,
      ],
      imports: [AuthModule],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DialogLocationInfoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
