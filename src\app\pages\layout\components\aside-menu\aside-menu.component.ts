import { Component, HostListener, OnInit } from '@angular/core';

@Component({
  selector: 'app-aside-menu',
  templateUrl: './aside-menu.component.html',
  styleUrls: ['./aside-menu.component.css']
})
export class AsideMenuComponent implements OnInit {

  screenHeight: any;

  constructor() {
    this.getScreenSize();
   }

  @HostListener('window:resize', ['$event'])
  getScreenSize(event?: any){
    this.screenHeight = window.innerHeight-100;
    this.screenHeight = this.screenHeight + "px";
  }

  ngOnInit(): void {
  }

}
