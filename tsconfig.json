{"compileOnSave": false, "compilerOptions": {"paths": {"@app/*": ["src/app/*"], "@src/*": ["src/*"], "@core/*": ["src/app/core/*"], "@page/*": ["src/app/pages/*"], "@material/*": ["src/app/material/*"], "@location/*": ["src/app/location/*"], "@notification/*": ["src/app/notification/*"], "@delivery/*": ["src/app/delivery/*"], "@services/*": ["src/app/services/*"], "@shared/*": ["src/app/shared/*"], "@auth/*": ["src/app/auth/*"]}, "baseUrl": "./", "outDir": "./dist/out-tsc", "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "sourceMap": true, "declaration": false, "downlevelIteration": true, "experimentalDecorators": true, "moduleResolution": "node", "importHelpers": true, "target": "es2015", "module": "es2020", "lib": ["es2018", "dom"], "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "strictPropertyInitialization": false}, "angularCompilerOptions": {"strictInjectionParameters": true, "strictInputAccessModifiers": true, "strictTemplates": true, "enableIvy": false}}