import {
  Component,
  OnInit,
  AfterViewInit,
  ViewChild,
  OnDestroy,
  Output,
  EventEmitter,
  ViewEncapsulation,
} from '@angular/core';
import { Subject, of } from 'rxjs';
import { catchError, takeUntil } from 'rxjs/operators';

import { MatPaginator } from '@angular/material/paginator';
import { MatTableDataSource } from '@angular/material/table';
import { MatDialog } from '@angular/material/dialog';

import {
  LocationCategory,
  LocationStatus,
  Location,
} from '@app/core/interfaces/location.interface';
import { LocationService } from '@app/services/location/location.service';
import { NotificationsService } from '@shared/services/notifications.service';

import { DialogComponent } from '@app/shared/components/dialog/dialog.component';

import {
  GetSortOrder,
  GetSortOrderObject,
  RemoveElementArray,
  ReplaceElementArray,
  ReplaceElementObjectArray,
} from '@app/shared/utils/utils';
import { LayoutDialogLocationComponent } from '../layout-dialog-location/layout-dialog-location.component';
import { LayoutLocationComponent } from '../layout-location/layout-location.component';
import { locationDataType } from '@app/location/enum/enums';
import { FormControl } from '@angular/forms';
import { LOCATION_ACTIVE_STATUS } from '@app/location/mock/status';
import { typeAction } from '@app/shared/utils/enum';
import { DialogLocationEditNameComponent } from '../dialog-location-edit-name/dialog-location-edit-name.component';
import { DialogLocationStatusComponent } from '../dialog-location-status/dialog-location-status.component';
import { environment } from '@src/environments/environment';
import { UserService } from '@app/services/user/user.service';

@Component({
  selector: 'app-location-active-list',
  templateUrl: './location-active-list.component.html',
  styleUrls: ['./location-active-list.component.css'],
  encapsulation: ViewEncapsulation.None,
})
export class LocationActiveListComponent
  implements OnInit, OnDestroy, AfterViewInit {
  displayedColumns: string[] = [
    'name',
    'category',
    'status',
  ];
  dataSource = new MatTableDataSource<Location>();
  subject = new Subject<any>();
  data: Location[] = [];
  filterCategory: LocationCategory[] = [];
  filterStatus: LocationStatus[] = [];
  status = locationDataType;
  orderDescending = false;

  categoryFilter = new FormControl('');
  statusFilter = new FormControl('');
  nameFilter = new FormControl('');
  filterValues = {
    category: '',
    status: '',
    name: '',
  };

  locationId: number;

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @Output() eventData = new EventEmitter<boolean>();

  constructor(
    private locationService: LocationService,
    private userService: UserService,
    private layoutLocationComponent: LayoutLocationComponent,
    public dialog: MatDialog,
    private notificationsAlerts: NotificationsService
  ) { }

  ngOnInit(): void {
    this.loadData();
    this.listenDataSubject();

    // Filter

    this.locationService
      .getLocationsCategoriesList()
      .pipe(
        takeUntil(this.subject),
        catchError((err) => {
          this.notificationsAlerts.openSnackBar('Error', 'Ok');
          return of(err);
        })
      )
      .subscribe((res) => {
        this.filterCategory = res;
      });

    this.locationService
      .getLocationsStatusList()
      .pipe(
        takeUntil(this.subject),
        catchError((err) => {
          this.notificationsAlerts.openSnackBar('Error', 'Ok');
          return of(err);
        })
      )
      .subscribe((res) => {
        this.filterStatus = res;
      });

    this.fieldListener();
  }

  private listenDataSubject(): void {
    this.locationService
      .listenDataSubjectLocations()
      .pipe(
        takeUntil(this.subject),
        catchError((err) => {
          this.notificationsAlerts.openSnackBar('Error', 'Ok');
          return of(err);
        })
      )
      .subscribe((res) => {
        if (this.locationService.action === typeAction.updated) {
          this.data = this.dataSource.data = ReplaceElementArray(
            this.data,
            res
          );
        }
      });

    this.userService
      .listenDataSubjectUser()
      .pipe(
        takeUntil(this.subject),
        catchError((err) => {
          this.notificationsAlerts.openSnackBar('Error', 'Ok');
          return of(err);
        })
      )
      .subscribe((res) => {
        if (this.userService.action === typeAction.updated) {
          this.data = this.dataSource.data = ReplaceElementObjectArray(
            this.data,
            this.locationId,
            res,
            'owner'
          );
        }
      });

    // this.nodeService
    //   .listenDataSubjectNode()
    //   .pipe(
    //     takeUntil(this.subject),
    //     catchError((err) => {
    //       this.notificationsAlerts.openSnackBar('Error', 'Ok');
    //       return of(err);
    //     })
    //   )
    //   .subscribe((res) => {
    //     if (this.nodeService.action === typeAction.updated) {
    //       this.data = this.dataSource.data = ReplaceElementObjectArray(
    //         this.data,
    //         this.locationId,
    //         res,
    //         'node'
    //       );
    //     }
    //   });
  }

  ngAfterViewInit(): void {
    this.dataSource.filterPredicate = this.createFilter();
    this.dataSource.paginator = this.paginator;
  }

  loadData(): void {
    this.layoutLocationComponent.subject.subscribe((data: Location[]) => {
      this.data = this.dataSource.data = data.filter((data) => {
        return LOCATION_ACTIVE_STATUS.includes(data.locationStatus.name);
      });
    });
  }

  private createFilter(): (location: Location, filter: string) => boolean {
    let filterFunction = function (location: any, filter: any): boolean {
      let searchTerms = JSON.parse(filter);

      let qualifies: boolean = true;

      if (searchTerms.name) {
        qualifies =
          location.name &&
          location.name
            .toLowerCase()
            .indexOf(searchTerms.name.toLowerCase()) !== -1;
      }

      if (searchTerms.category) {
        qualifies =
          qualifies &&
          location.locationCategory &&
          location.locationCategory.name &&
          location.locationCategory.name.indexOf(searchTerms.category) !==
          -1;
      }

      if (searchTerms.status) {
        qualifies =
          qualifies &&
          location.locationStatus &&
          location.locationStatus.name &&
          location.locationStatus.name.indexOf(searchTerms.status) !== -1;
      }

      return qualifies;
    };

    return filterFunction;
  }

  private fieldListener() {
    this.nameFilter.valueChanges.subscribe((name) => {
      this.filterValues.name = name;
      this.dataSource.filter = JSON.stringify(this.filterValues);
    });

    this.categoryFilter.valueChanges.subscribe((category) => {
      this.filterValues.category = category;
      this.dataSource.filter = JSON.stringify(this.filterValues);
    });

    this.statusFilter.valueChanges.subscribe((status) => {
      this.filterValues.status = status;
      this.dataSource.filter = JSON.stringify(this.filterValues);
    });
  }

  clearfilters(): void {
    this.nameFilter.setValue('');
    this.categoryFilter.setValue('');
    this.statusFilter.setValue('');
    this.filterValues.category = '';
    this.filterValues.status = '';
    this.filterValues.name = '';
  }

  orderBy(column: string): void {
    this.data = this.dataSource.data = this.data.sort(GetSortOrder(column));
  }

  orderByObject(keyObject: string, column: string): void {
    this.data = this.dataSource.data = this.data.sort(
      GetSortOrderObject(keyObject, column)
    );
  }

  openDialogEdit(row: Location): void {
    this.dialog.open(LayoutDialogLocationComponent, {
      height: 'auto',
      width: '600px',
      data: row,
    });
  }

  openDialogUpdate(row: Location): void {
    this.dialog.open(DialogLocationEditNameComponent, {
      height: 'auto',
      width: '600px',
      data: row,
    });
  }

  openDialogNotification(row: Location): void {
    
  }

  openDialogStatus(row: Location): void {
    this.dialog.open(DialogLocationStatusComponent, {
      height: 'auto',
      width: '600px',
      data: row,
    });
  }

  


  ngOnDestroy(): void {
    this.subject.complete();
    this.subject.unsubscribe();
  }
}
