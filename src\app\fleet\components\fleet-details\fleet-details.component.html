<div class="mat-elevation-z2 sub-content layout-style" *ngIf="dataGlider">
  <div class="d-flex align-items-center mb-3 justify-content-end">
    <button
      mat-raised-button
      color="accent"
      class="request-maintenance-btn"
      (click)="openRequestMaintenanceDialog()"
    >
      <mat-icon>build</mat-icon>
      Request Maintenance
    </button>
  </div>

  <div class="row mb-3">
    <div class="col-md-4 col-12 mb-2">
      <p>
        <span class="data-label">Glider ID:</span>
        <span class="data-value">{{ dataGlider.id || 'No data' }}</span>
      </p>
      <p>
        <span class="data-label">Glider Name:</span>
        <span class="data-value">{{ dataGlider.name || 'No data' }}</span>
      </p>
      <p>
        <span class="data-label">IP:</span>
        <span class="data-value">{{ dataGlider.vpnIp || 'No data' }}</span>
        <button
          mat-icon-button
          *ngIf="dataGlider.vpnIp"
          (click)="copyToClipboard(dataGlider.vpnIp)"
          class="copy-button"
          matTooltip="Copy IP address">
          <mat-icon class="small-icon">content_copy</mat-icon>
        </button>
      </p>

      <p>
        <span class="data-label">Network ID:</span>
        <span class="data-value">{{ dataGlider.vpnNetworkId || 'No data' }}</span>
      </p>
      <p>
        <span class="data-label">Total Flights:</span>
        <span class="data-value">{{ totalFlights ?? 'No data' }}</span>
      </p>
      <p class="d-flex align-items-center">
        <span class="data-label">Pixhawk:</span>
        <span class="d-flex align-items-center">
        {{ dataGlider.pixhawkUuid || 'No data' }}
  </span>
        <button
          mat-icon-button
          *ngIf="dataGlider.pixhawkUuid"
          (click)="copyToClipboard(dataGlider.pixhawkUuid)"
          class="copy-button"
          matTooltip="Copy Pixhawk UUID">
          <mat-icon class="small-icon"
          >content_copy</mat-icon>
        </button>
      </p>
    </div>

    <div class="col-md-4 col-12 mb-2">
      <p>
        <span class="data-label">Region:</span>
        <span class="data-value">
          {{ dataGlider.region?.country || 'No data' }}
        </span>
      </p>
      <p>
        <span class="data-label">Company:</span>
        <span class="data-value">{{ dataGlider.company?.name || 'No data' }}</span>
      </p>
      <p>
        <span class="data-label">Manufacturing Date:</span>
        <span class="data-value">
          {{
            dataGlider.manufacturingDate
              ? (dataGlider.manufacturingDate | date: 'dd/MM/yyyy')
              : 'No data'
          }}
        </span>
      </p>
    </div>

    <div class="col-md-4 col-12 mb-2">
      <p class="data-label">Status:</p>
      <div class="d-flex align-items-center">
        <div
          class="status-chip status-filled d-inline-flex align-items-center"
          [ngStyle]="{'background-color': '#' + (dataGlider.gliderStatus?.colorHexcode || '808080')}"
          [matMenuTriggerFor]="statusMenu"
          [matTooltip]="getLastStatusChangeTooltip()"
          matTooltipPosition="above"
          matTooltipClass="status-tooltip"
        >
          <span class="status-text">{{ dataGlider.gliderStatus?.name || 'Unavailable' }}</span>
          <mat-icon>arrow_drop_down</mat-icon>
        </div>
        <button 
          mat-icon-button 
          class="ms-2" 
          matTooltip="View status history"
          (click)="scrollToStatusHistory()"
        >
          <mat-icon>history</mat-icon>
        </button>
      </div>
      <mat-menu #statusMenu="matMenu">
        <button
          mat-menu-item
          *ngFor="let s of allStatuses"
          (click)="updateStatus(s)"
        >
          <div class="menu-item-content d-flex align-items-center">
            <span class="status-circle" [ngClass]="getStatusClass(s)"></span>
            <span class="menu-item-text ms-2">{{ s }}</span>
          </div>
        </button>
      </mat-menu>

      <div class="mt-3">
        <p>
          <span class="data-label">Total Flight Hours:</span>
          <span class="data-value">
  {{
              dataGlider?.totalFlightTimeInSeconds != null
                ? ((dataGlider?.totalFlightTimeInSeconds || 0) | formattedFlightTime)
                : 'No data'
            }}
</span>
        </p>
        <p>
          <span class="data-label">Flight Hours Since Last Maintenance:</span>
          <span class="data-value">
   {{
              dataGlider?.totalFlightTimeSinceLastMaintenanceInSeconds != null
                ? ((dataGlider.totalFlightTimeSinceLastMaintenanceInSeconds || 0) | formattedFlightTime)
                : 'No data'
            }}
  </span>
        </p>
      </div>
    </div>
  </div>

  <div class="software-card mt-3">
    <h2>Software Versions</h2>
    <mat-card-content>
      <div class="row">
        <div class="col-md-6 col-12 mb-3">
          <h5 class="software-group-title">Avionics</h5>

          <div class="software-item mb-3">
            <div class="label-row">
              <strong>Jetson SW (current):</strong>
              <span class="software-version-badge">
                {{ dataGlider.jetsonSoftwareVersion?.name || 'No data' }}
              </span>
            </div>

            <div class="desired-row d-flex align-items-center">
              <span class="desired-label">Jetson SW (desired):</span>
              <span class="d-flex align-items-center">
            <button
              mat-button
              color="primary"
              [matMenuTriggerFor]="jetsonMenu"
              [class.warning-border]="showJetsonWarning"
              class="ms-2"
            >
              {{ dataGlider.desiredJetsonSoftwareVersion?.name || 'Select version' }}
              <mat-icon>arrow_drop_down</mat-icon>
            </button>

            <mat-icon
              *ngIf="showJetsonWarning"
              class="icon-yellow ms-2"
              matTooltip="Version mismatch. Please update the device manually to apply the desired version."

            >
              warning_amber
            </mat-icon>
             </span>
            </div>

            <mat-menu #jetsonMenu="matMenu" class="menu-with-search">
              <div class="search-container" (mousedown)="$event.stopPropagation()" (click)="$event.stopPropagation()">
                <mat-form-field appearance="fill" class="search-field">
                  <mat-label>Search version</mat-label>
                  <input
                    matInput
                    placeholder="Search..."
                    (keyup)="searchJetson($any($event.target).value)"
                  />
                </mat-form-field>
              </div>
              <button
                mat-menu-item
                *ngFor="let v of filteredJetsonList"
                (click)="selectJetson(v)"
              >
                {{ v.name }}
              </button>
              <button mat-menu-item (click)="goToCreateVersion('jetson')">
                <mat-icon>add</mat-icon>
                Add new version
              </button>
            </mat-menu>
          </div>

          <div class="software-item mb-3">
            <div class="label-row">
              <strong>Autopilot SW (current):</strong>
              <span class="software-version-badge">
                {{ dataGlider.autopilotSoftwareVersion?.name || 'No data' }}
              </span>
            </div>

            <div class="desired-row d-flex align-items-center">
              <span class="desired-label">Autopilot SW (desired):</span>
              <span class="d-flex align-items-center">
              <button
                mat-button
                color="primary"
                [matMenuTriggerFor]="autopilotMenu"
                [class.warning-border]="showAutopilotWarning"
                class="ms-2"
              >
                {{ dataGlider.desiredAutopilotSoftwareVersion?.name || 'Select version' }}
                <mat-icon>arrow_drop_down</mat-icon>
              </button>

              <mat-icon
                *ngIf="showAutopilotWarning"
                class="icon-yellow ms-2"
                matTooltip="Version mismatch. Please update the device manually to apply the desired version."
              >
                warning_amber
              </mat-icon>
              </span>
            </div>

            <mat-menu #autopilotMenu="matMenu" class="menu-with-search">
              <div class="search-container" (mousedown)="$event.stopPropagation()" (click)="$event.stopPropagation()">
                <mat-form-field appearance="fill" class="search-field">
                  <mat-label>Search version</mat-label>
                  <input
                    matInput
                    placeholder="Search..."
                    (keyup)="searchAutopilot($any($event.target).value)"
                  />
                </mat-form-field>
              </div>
              <button
                mat-menu-item
                *ngFor="let v of filteredAutopilotList"
                (click)="selectAutopilot(v)"
              >
                {{ v.name }}
              </button>
              <button mat-menu-item (click)="goToCreateVersion('autopilot')">
                <mat-icon>add</mat-icon>
                Add new version
              </button>
            </mat-menu>
          </div>
        </div>

        <div class="col-md-6 col-12 mb-3">
          <h5 class="software-group-title">FTS</h5>

          <div class="software-item mb-3">
            <div class="label-row">
              <strong>FTS Pixhawk SW (current):</strong>
              <span class="software-version-badge">
                {{ dataGlider.ftsPixhawkSoftwareVersion?.name || 'No data' }}
              </span>
            </div>

            <div class="desired-row d-flex align-items-center">

              <span class="desired-label">FTS Pixhawk SW (desired):</span>
              <span class="d-flex align-items-center">
              <button
                mat-button
                color="primary"
                [matMenuTriggerFor]="ftsPixhawkMenu"
                [class.warning-border]="showFtsPixhawkWarning"
                class="ms-2"
              >
                {{ dataGlider.desiredFtsPixhawkSoftwareVersion?.name || 'Select version' }}
                <mat-icon>arrow_drop_down</mat-icon>
              </button>

              <mat-icon
                *ngIf="showFtsPixhawkWarning"
                class="icon-yellow ms-2"
                matTooltip="Version mismatch. Please update the device manually to apply the desired version."

              >
                warning_amber
              </mat-icon>
              </span>
            </div>

            <mat-menu #ftsPixhawkMenu="matMenu" class="menu-with-search">
              <div
                class="search-container"
                (mousedown)="$event.stopPropagation()"
                (click)="$event.stopPropagation()"
              >
                <mat-form-field appearance="fill" class="search-field">
                  <mat-label>Search version</mat-label>
                  <input
                    matInput
                    placeholder="Search..."
                    (keyup)="searchFtsPixhawk($any($event.target).value)"
                  />
                </mat-form-field>
              </div>
              <button
                mat-menu-item
                *ngFor="let v of filteredFtsPixhawkList"
                (click)="selectFtsPixhawk(v)"
              >
                {{ v.name }}
              </button>
              <button mat-menu-item (click)="goToCreateVersion('ftsPixhawk')">
                <mat-icon>add</mat-icon>
                Add new version
              </button>
            </mat-menu>
          </div>

          <div class="software-item mb-3">
            <div class="label-row">
              <strong>FTS Raspi SW (current):</strong>
              <span class="software-version-badge">
                {{ dataGlider.ftsRaspiSoftwareVersion?.name || 'No data' }}
              </span>
            </div>

            <div class="desired-row d-flex align-items-center">
              <span class="desired-label">FTS Raspi SW (desired):</span>
              <span class="d-flex align-items-center">
              <button
                mat-button
                color="primary"
                [matMenuTriggerFor]="ftsRaspiMenu"
                [class.warning-border]="showFtsRaspiWarning"
                class="ms-2"
              >
                {{ dataGlider.desiredFtsRaspiSoftwareVersion?.name || 'Select version' }}
                <mat-icon>arrow_drop_down</mat-icon>
              </button>

              <mat-icon
                *ngIf="showFtsRaspiWarning"
                class="icon-yellow ms-2"
                matTooltip="Version mismatch. Please update the device manually to apply the desired version."
              >
                warning_amber
              </mat-icon>
              </span>
            </div>

            <mat-menu #ftsRaspiMenu="matMenu" class="menu-with-search">
              <div
                class="search-container"
                (mousedown)="$event.stopPropagation()"
                (click)="$event.stopPropagation()"
              >
                <mat-form-field appearance="fill" class="search-field">
                  <mat-label>Search version</mat-label>
                  <input
                    matInput
                    placeholder="Search..."
                    (keyup)="searchFtsRaspi($any($event.target).value)"
                  />
                </mat-form-field>
              </div>
              <button
                mat-menu-item
                *ngFor="let v of filteredFtsRaspiList"
                (click)="selectFtsRaspi(v)"
              >
                {{ v.name }}
              </button>
              <button mat-menu-item (click)="goToCreateVersion('ftsRaspi')">
                <mat-icon>add</mat-icon>
                Add new version
              </button>
            </mat-menu>
          </div>
        </div>
      </div>
    </mat-card-content>
  </div>
</div>

<div class="mat-elevation-z2 sub-content sub-content-up layout-style" *ngIf="dataGlider">
  <h3>
    <ng-container *ngIf="maintenanceHistory && maintenanceHistory.length >= 5; else lessOrEqualFive">
      Last 5 Maintenances
    </ng-container>
    <ng-template #lessOrEqualFive>
      {{ maintenanceHistory?.length || 0 }} Maintenances
    </ng-template>
  </h3>

  <div class="loader" *ngIf="isLoadingMaintenance">
    <mat-progress-spinner mode="indeterminate"></mat-progress-spinner>
  </div>
  <div class="w-100">
    <table
      *ngIf="!isLoadingMaintenance && maintenanceHistory && maintenanceHistory.length > 0"
      mat-table
      [dataSource]="maintenanceHistory"
      class="fixed-table"
      style="margin-top: 10px;"
    >
      <ng-container matColumnDef="dueDate">
        <th mat-header-cell *matHeaderCellDef>Due Date</th>
        <td mat-cell *matCellDef="let element">{{ element.dueDate | date:'mediumDate' }}</td>
      </ng-container>

      <ng-container matColumnDef="completedDate">
        <th mat-header-cell *matHeaderCellDef>Completed Date</th>
        <td mat-cell *matCellDef="let element">
          {{ element.completedDate ? (element.completedDate | date:'mediumDate') : 'N/A' }}
        </td>
      </ng-container>

      <ng-container matColumnDef="maintenanceType">
        <th mat-header-cell *matHeaderCellDef>Type</th>
        <td mat-cell *matCellDef="let element">{{ element.maintenanceType?.name || 'N/A' }}</td>
      </ng-container>

      <ng-container matColumnDef="maintenanceStaff">
        <th mat-header-cell *matHeaderCellDef>Staff</th>
        <td mat-cell *matCellDef="let element">{{ element.maintenanceStaff || 'N/A' }}</td>
      </ng-container>

      <ng-container matColumnDef="notes">
        <th mat-header-cell *matHeaderCellDef>Notes</th>
        <td mat-cell *matCellDef="let element">{{ element.notes || 'No notes' }}</td>
      </ng-container>

      <tr
        mat-header-row
        *matHeaderRowDef="['dueDate','completedDate','maintenanceType','maintenanceStaff','notes']"
      ></tr>
      <tr
        mat-row
        *matRowDef="let row; columns: ['dueDate','completedDate','maintenanceType','maintenanceStaff','notes'];"
      ></tr>
    </table>
  </div>

  <div *ngIf="!isLoadingMaintenance && maintenanceHistory && maintenanceHistory.length === 0">
    <p>No maintenance history available.</p>
  </div>
</div>


<div class="mat-elevation-z2 sub-content sub-content-up layout-style">
  <h3>
    <ng-container *ngIf="lastIncidents && lastIncidents.length >= 5; else lessOrEqualFive">
      Last 5 Incidents
    </ng-container>
    <ng-template #lessOrEqualFive>
      {{ lastIncidents?.length || 0 }} Incident
    </ng-template>
  </h3>
  <div class="d-flex justify-content-center my-3" *ngIf="isLoadingIncidents">
    <mat-spinner></mat-spinner>
  </div>
  <ng-container *ngIf="!isLoadingIncidents">
    <ng-container *ngIf="(lastIncidents?.length || 0) > 0; else noAccidents">
      <table mat-table [dataSource]="lastIncidents" class="fixed-table" style="margin-top: 10px;">

        <ng-container matColumnDef="incidentName">
          <th mat-header-cell *matHeaderCellDef>Incident Name</th>
          <td mat-cell *matCellDef="let flight">
            {{ flight.incident?.name }}
          </td>
        </ng-container>

        <ng-container matColumnDef="aircraftDamage">
          <th mat-header-cell *matHeaderCellDef>Aircraft Damage</th>
          <td mat-cell *matCellDef="let flight">
            {{ flight.incident_aircraft_damage || 'N/A' }}
          </td>
        </ng-container>

        <ng-container matColumnDef="accidentDescription">
          <th mat-header-cell *matHeaderCellDef>Description</th>
          <td mat-cell *matCellDef="let flight">
            {{ flight.incident_description || 'N/A' }}
          </td>
        </ng-container>

        <ng-container matColumnDef="accidentInjuries">
          <th mat-header-cell *matHeaderCellDef>Injuries</th>
          <td mat-cell *matCellDef="let flight">
            {{ flight.incident_injuries || 'None' }}
          </td>
        </ng-container>

        <tr mat-header-row
            *matHeaderRowDef="['incidentName','aircraftDamage','accidentDescription','accidentInjuries']"></tr>
        <tr mat-row
            *matRowDef="let row; columns: ['incidentName','aircraftDamage','accidentDescription','accidentInjuries'];"></tr>
      </table>
    </ng-container>
  </ng-container>

  <ng-template #noAccidents>
    <p>No Incidents to display.</p>
  </ng-template>
</div>

<div class="mat-elevation-z2 sub-content sub-content-up layout-style">
  <h3>
    <ng-container *ngIf="lastFlights && lastFlights.length >= 5; else lessFiveFlights">
      Last 5 Flights
    </ng-container>
    <ng-template #lessFiveFlights>
      {{ lastFlights?.length || 0 }} Flights
    </ng-template>
  </h3>

  <div class="d-flex justify-content-center my-3" *ngIf="isLoadingLastFlights">
    <mat-spinner></mat-spinner>
  </div>

  <ng-container *ngIf="!isLoadingLastFlights">
    <ng-container *ngIf="(lastFlights?.length || 0) > 0; else noLastFlights">
      <table mat-table [dataSource]="lastFlights.slice(0,5)" class="fixed-table" style="margin-top: 10px;">

        <ng-container matColumnDef="id">
          <th mat-header-cell *matHeaderCellDef>Flight ID</th>
          <td mat-cell *matCellDef="let flight">
            {{ flight.id }}
          </td>
        </ng-container>

        <ng-container matColumnDef="start_time">
          <th mat-header-cell *matHeaderCellDef>Start Time</th>
          <td mat-cell *matCellDef="let flight">
            {{ flight.start_time | epochToDate:'short' }}
          </td>
        </ng-container>

        <ng-container matColumnDef="duration">
          <th mat-header-cell *matHeaderCellDef>Duration</th>
          <td mat-cell *matCellDef="let flight">
            {{ flight.duration | secondsToHms }}
          </td>
        </ng-container>

        <ng-container matColumnDef="distance">
          <th mat-header-cell *matHeaderCellDef>Distance (m)</th>
          <td mat-cell *matCellDef="let flight">
            {{ flight.distance_in_meters | number:'1.0-0' }}
          </td>
        </ng-container>

        <ng-container matColumnDef="start_location">
          <th mat-header-cell *matHeaderCellDef>Start Location</th>
          <td mat-cell *matCellDef="let flight">
            {{ flight.start_location }}
          </td>
        </ng-container>

        <ng-container matColumnDef="end_location">
          <th mat-header-cell *matHeaderCellDef>End Location</th>
          <td mat-cell *matCellDef="let flight">
            {{ flight.end_location }}
          </td>
        </ng-container>


        <tr mat-header-row *matHeaderRowDef="[
          'id',
          'start_time',
          'duration',
          'distance',
          'start_location',
          'end_location']

        ">
        </tr>
        <tr mat-row
            *matRowDef="let row; columns: [
            'id',
            'start_time',
            'duration',
            'distance',
            'start_location',
            'end_location']

          ">
        </tr>
      </table>
    </ng-container>

    <ng-template #noLastFlights>
      <p>No flights to display.</p>
    </ng-template>
  </ng-container>
</div>

<div class="mat-elevation-z2 sub-content sub-content-up layout-style">
  <h3>Next 5 Flights (Scheduled)</h3>

  <div class="d-flex justify-content-center my-3" *ngIf="isLoadingNextFlights">
    <mat-spinner></mat-spinner>
  </div>

  <ng-container *ngIf="!isLoadingNextFlights">
    <ng-container *ngIf="(nextFlights?.length || 0) > 0; else noNextFlights">
      <table mat-table [dataSource]="nextFlights" class="fixed-table" style="margin-top: 10px;">

        <ng-container matColumnDef="id">
          <th mat-header-cell *matHeaderCellDef>Flight ID</th>
          <td mat-cell *matCellDef="let flight">{{ flight.id }}</td>
        </ng-container>

        <ng-container matColumnDef="start_time">
          <th mat-header-cell *matHeaderCellDef>Start Time</th>
          <td mat-cell *matCellDef="let flight">
            {{ flight.start_time | epochToDate:'short' }}
          </td>
        </ng-container>

        <ng-container matColumnDef="duration">
          <th mat-header-cell *matHeaderCellDef>Duration</th>
          <td mat-cell *matCellDef="let flight">
            {{ flight.duration | secondsToHms }}
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="['id', 'start_time', 'duration']"></tr>
        <tr mat-row *matRowDef="let row; columns: ['id', 'start_time', 'duration'];"></tr>
      </table>
    </ng-container>
    <ng-template #noNextFlights>
      <p>No upcoming flights scheduled.</p>
    </ng-template>
  </ng-container>
</div>

<div id="statusHistorySection" class="mat-elevation-z2 sub-content sub-content-up layout-style">
  <h3>
    <ng-container *ngIf="statusLogs && statusLogs.length >= 5; else lessFiveStatusLogs">
      Last 5 Status Changes
    </ng-container>
    <ng-template #lessFiveStatusLogs>
      {{ statusLogs?.length || 0 }} Status Changes
    </ng-template>
  </h3>

  <div class="d-flex justify-content-center my-3" *ngIf="isLoadingStatusLogs">
    <mat-spinner></mat-spinner>
  </div>

  <ng-container *ngIf="!isLoadingStatusLogs">
    <ng-container *ngIf="(statusLogs?.length || 0) > 0; else noStatusLogs">
      <div class="w-100">
        <table mat-table [dataSource]="statusLogs" class="fixed-table w-100" style="margin-top: 10px; width: 100%; table-layout: fixed;">
          <ng-container matColumnDef="date">
            <th mat-header-cell *matHeaderCellDef style="width: 20%">Date</th>
            <td mat-cell *matCellDef="let log">
              {{ log.createdAt | date:'dd/MM/yyyy HH:mm' }}
            </td>
          </ng-container>

          <ng-container matColumnDef="user">
            <th mat-header-cell *matHeaderCellDef style="width: 20%">User</th>
            <td mat-cell *matCellDef="let log">
              {{ log.userEmail }}
            </td>
          </ng-container>

          <ng-container matColumnDef="statusChange">
            <th mat-header-cell *matHeaderCellDef style="width: 40%">Status Change</th>
            <td mat-cell *matCellDef="let log">
              <div class="d-flex align-items-center">
                <div class="status-chip status-filled d-inline-flex align-items-center"
                     [ngStyle]="{'background-color': '#' + (log.oldGliderStatus?.colorHexcode || '808080')}">
                  {{ log.oldGliderStatus?.name || 'N/A' }}
                </div>
                <mat-icon class="mx-2">arrow_forward</mat-icon>
                <div class="status-chip status-filled d-inline-flex align-items-center"
                     [ngStyle]="{'background-color': '#' + (log.newGliderStatus?.colorHexcode || '808080')}">
                  {{ log.newGliderStatus?.name || 'N/A' }}
                </div>
              </div>
            </td>
          </ng-container>

          <ng-container matColumnDef="note">
            <th mat-header-cell *matHeaderCellDef style="width: 20%">Note</th>
            <td mat-cell *matCellDef="let log">
              {{ log.note || 'N/A' }}
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="['date', 'user', 'statusChange', 'note']"></tr>
          <tr mat-row 
              *matRowDef="let row; columns: ['date', 'user', 'statusChange', 'note'];"
              [ngClass]="{'current-status-row': isCurrentStatus(row.newGliderStatus?.id)}"></tr>
        </table>
      </div>
    </ng-container>
  </ng-container>

  <ng-template #noStatusLogs>
    <p>No status history available.</p>
  </ng-template>
</div>

