import { CommonModule, DatePipe } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { MAT_DIALOG_DEFAULT_OPTIONS, MatDialogModule } from '@angular/material/dialog';
import { MaterialModule } from '@app/material/material.module';
import { FleetService } from '@app/services/fleet/fleet.service';
import { RideService } from '@app/services/ride/ride.service';
import { RideTimingService } from '@app/services/ride-timing/ride-timing.service';
import { TimeTrackingService } from '@app/services/time-tracking/time-tracking.service';
import { CancellationReasonService } from '@app/services/cancellation-reason/cancellation-reason.service';
import { RouteService } from '@app/services/route/route.service';
import { UserService } from '@app/services/user/user.service';
import { SharedModule } from '@app/shared/shared.module';
import { DialogRideGliderComponent } from './components/dialog-ride-glider/dialog-ride-glider.component';
import { DialogRideOperatorComponent } from './components/dialog-ride-operator/dialog-ride-operator.component';
import { DialogRideComponent } from './components/dialog-ride/dialog-ride.component';
import { DialogRideExportCSVComponent } from './components/dialog-ride-export-csv/dialog-ride-export-csv.component';
import { LayoutRideInfoComponent } from './components/layout-ride-info/layout-ride-info.component';
import { RideListComponent } from './components/ride-list/ride-list.component';
import { RideTimingHistoryComponent } from './components/ride-timing-history/ride-timing-history.component';
import { TimeTrackingDescriptionDialogComponent } from './components/time-tracking-description-dialog/time-tracking-description-dialog.component';
import { RideRoutingModule } from './ride-routing.module';
import { LocationService } from '../services/location/location.service';
import {CancelReasonDialogComponent} from '@app/ride/components/dialog-cancel-reason/cancel-reason-dialog.component';
import {DeleteRideDialogComponent} from './components/dialog-delete-ride/delete-ride-dialog.component';

@NgModule({
  declarations: [
    RideListComponent,
    DialogRideComponent,
    DialogRideOperatorComponent,
    DialogRideGliderComponent,
    LayoutRideInfoComponent,
    DialogRideExportCSVComponent,
    CancelReasonDialogComponent,
    DeleteRideDialogComponent,
    RideTimingHistoryComponent,
    TimeTrackingDescriptionDialogComponent
  ],
  imports: [
    CommonModule,
    RideRoutingModule,
    MaterialModule,
    SharedModule,
    ReactiveFormsModule,
    FormsModule,
    MatDialogModule
  ],
  providers: [
    {
      provide: MAT_DIALOG_DEFAULT_OPTIONS,
      useValue: { hasBackdrop: true },
    },
    RideService,
    UserService,
    FleetService,
    DatePipe,
    LocationService,
    RouteService,
    CancellationReasonService,
  ],
  entryComponents: [
    TimeTrackingDescriptionDialogComponent
  ],
})
export class RideModule {}
