import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MaterialModule } from '@material/material.module';
import { LocationRoutingModule } from './location-routing.module';
import { SharedModule } from '@app/shared/shared.module';

import { MAT_DIALOG_DEFAULT_OPTIONS } from '@angular/material/dialog';

import { LocationService } from '../services/location/location.service';
import { ReactiveFormsModule } from '@angular/forms';

import { LayoutLocationComponent } from './components/layout-location/layout-location.component';
import { LocationActiveListComponent } from './components/location-active-list/location-active-list.component';
import { DialogLocationEditNameComponent } from './components/dialog-location-edit-name/dialog-location-edit-name.component';
import { LayoutDialogLocationComponent } from './components/layout-dialog-location/layout-dialog-location.component';
import { UserService } from '@app/services/user/user.service';
import { DialogLocationStatusComponent } from './components/dialog-location-status/dialog-location-status.component';
import { DialogLocationEditComponent } from './components/dialog-location-edit/dialog-location-edit.component';

@NgModule({
  declarations: [
    LayoutLocationComponent,
    LayoutDialogLocationComponent,
    LocationActiveListComponent,
    DialogLocationEditNameComponent,
    DialogLocationEditComponent,
    DialogLocationStatusComponent,
  ],
  imports: [
    CommonModule,
    LocationRoutingModule,
    MaterialModule,
    SharedModule,
    ReactiveFormsModule,
  ],
  providers: [
    {
      provide: MAT_DIALOG_DEFAULT_OPTIONS,
      useValue: { hasBackdrop: true },
    },
    LocationService,
    UserService,
  ],
})
export class LocationModule {}
