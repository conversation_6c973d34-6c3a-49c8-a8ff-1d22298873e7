import {Component, OnInit, Inject, OnDestroy} from '@angular/core';
import {of, Subject, Observable} from 'rxjs';
import {FormControl, FormGroup} from '@angular/forms';
import {map, startWith} from 'rxjs/operators';

import {
  MAT_DIALOG_DATA,
  MatDialogRef,
  MatDialog,
} from '@angular/material/dialog';

import {Ride, RideExportFilters} from '@app/core/interfaces/ride.interface';
import {DatePipe} from '@angular/common';
import {RideService} from '@app/services/ride/ride.service';
import {FleetService} from '@app/services/fleet/fleet.service';
import {LocationService} from '@app/services/location/location.service';
import {Glider} from '@app/core/interfaces/glider.interace';
import {Location} from '@app/core/interfaces/location.interface';

@Component({
  selector: 'app-dialog-ride-export-csv',
  templateUrl: './dialog-ride-export-csv.component.html',
  styleUrls: ['./dialog-ride-export-csv.component.css'],
})
export class DialogRideExportCSVComponent implements OnInit, OnDestroy {
  form!: FormGroup;
  subject = new Subject<any>();
  filteredOptions: Observable<Glider[]>;
  filteredStartLocationOptions: Observable<Location[]>;
  filteredDestinationLocationOptions: Observable<Location[]>;

  dataGlider: Glider[];
  dataStartLocation: Location[];
  dataDestinationLocation: Location[];

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: RideExportFilters,
    // private notificationsAlerts: NotificationsService,
    private fleetService: FleetService,
    private locationService: LocationService,
    public dialogRef: MatDialogRef<DialogRideExportCSVComponent>,
    private rideService: RideService,
    public datepipe: DatePipe,
    public dialog: MatDialog
  ) {
    this.onForm();
  }

  onForm(): void {
    this.form = new FormGroup({
      start_date: new FormControl(this.data.start_date, []),
      end_date: new FormControl(this.data.end_date),
      glider: new FormControl(this.data.glider),
      start_location: new FormControl(this.data.start_location),
      destination_location: new FormControl(this.data.destination_location),
      include_package: new FormControl(this.data.include_package),
      include_no_package: new FormControl(this.data.include_no_package)
    });

  }

  displayFn(glider: Glider): string {
    return glider && glider.name ? glider.name : '';
  }

  displayFnLocation(location: Location): string {
    return location && location.name ? location.name : '';
  }

  ngOnInit(): void {
    this.fleetService.getFleetList().subscribe((res) => {
      this.dataGlider = res.data;

      this.filteredOptions = this.form.controls.glider.valueChanges.pipe(
        startWith(''),
        map((value) => {
          const note =
            typeof value === 'string' ? value : value?.name || value?.id;
          return note
            ? this._filter(note as string)
            : this.dataGlider?.slice();
        }));
    });

    this.locationService.getLocationsList().subscribe((res) => {
      this.dataStartLocation = res.data;

      this.filteredStartLocationOptions = this.form.controls.start_location.valueChanges.pipe(
        startWith(''),
        map((value) => {
          const note =
            typeof value === 'string' ? value : value?.name || value?.id;
          return note
            ? this._filterLocation(note as string)
            : this.dataStartLocation.slice();
        }));

      this.filteredDestinationLocationOptions = this.form.controls.destination_location.valueChanges.pipe(
        startWith(''),
        map((value) => {
          const note =

            typeof value === 'string' ? value : value?.name || value?.id;
          return note
            ? this._filterLocation(note as string)
            : this.dataStartLocation.slice();
        }));

    });


  }

  private _filter(value: string): Glider[] {
    const filterValue = value.toLowerCase();

    return this.dataGlider.filter(
      (glider) =>
        glider.name.toLowerCase().includes(filterValue) ||
        glider.id.toString().includes(filterValue)
    );
  }

  private _filterLocation(value: string): Location[] {
    const filterValue = value.toLowerCase();

    // return this.dataStartLocation.slice();

    return this.dataStartLocation.filter(
      (location) =>
        location.name.toLowerCase().includes(filterValue) ||
        location.id.toString().includes(filterValue)
    );
  }

  get formDialog(): any {
    return this.form.controls;
  }

  ngOnDestroy(): void {
    // this.subject.complete();
    // this.subject.unsubscribe();
  }

  create(): void {
    this.dialogRef.close();

    const response = this.rideService.getRideCSV(this.form.value).subscribe((x) => {
      // const blob = new Blob([x], { type: 'text/csv' });
      // const url= window.URL.createObjectURL(blob);
      // window.navigator.msSaveOrOpenBlob(blob, 'rides.csv');
      // window.location.assign(url);
      this.downLoadFile(x, 'rides.csv');
      return x;
    });
  }

  downLoadFile(data: string, fileName: string) {
    const anchorElement = document.createElement('a');

    document.body.appendChild(anchorElement);

    anchorElement.style.display = 'none';

    const blob = new Blob([data], {type: 'text/csv'});

    const url = window.URL.createObjectURL(blob);

    anchorElement.href = url;
    anchorElement.download = fileName;
    anchorElement.click();

    window.URL.revokeObjectURL(url);
  }
}
