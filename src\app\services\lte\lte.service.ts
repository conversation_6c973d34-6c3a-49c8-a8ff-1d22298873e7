import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { LTE } from '@app/core/interfaces/lte.interface';
import { Response } from '@app/core/interfaces/responses/response.interface';
import { typeAction } from '@app/shared/utils/enum';
import { environment } from '@src/environments/environment';
import { Observable, Subject } from 'rxjs';

@Injectable()
export class LteService {
  private apiUrl = environment.urlMsLte;
  private subjectLte = new Subject<LTE[]>();
  public action: typeAction | undefined;

  constructor(private http: HttpClient) {}

  getLteList(params?: HttpParams): Observable<Response<LTE[]>> {
    return this.http.get<Response<LTE[]>>(`${this.apiUrl}.json`, {
      params: params,
    });
  }

  createLte(lte: any): Observable<Response<LTE>> {
    return this.http.post<Response<LTE>>(`${this.apiUrl}.json`, lte);
  }

  updateLte(lteId: number, lte: any): Observable<Response<LTE>> {
    return this.http.patch<Response<LTE>>(`${this.apiUrl}/${lteId}.json`, lte);
  }

  notifyDataSubject(status: LTE[], action?: typeAction): void {
    this.action = action;
    this.subjectLte.next(status);
  }

  listenDataSubjectLte(): Observable<LTE[]> {
    return this.subjectLte.asObservable();
  }
}
