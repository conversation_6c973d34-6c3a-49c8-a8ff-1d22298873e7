import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'secondsToHms'
})
export class SecondsToHmsPipe implements PipeTransform {
  transform(value: number | null | undefined): string {
    if (!value || value <= 0) {
      return '0s';
    }
    const hours = Math.floor(value / 3600);
    const minutes = Math.floor((value % 3600) / 60);
    const seconds = (value % 60);

    let result = '';
    if (hours > 0) {
      result += hours + 'h ';
    }
    if (minutes > 0) {
      result += minutes + 'm ';
    }
    result += seconds + 's';

    return result.trim();
  }
}
