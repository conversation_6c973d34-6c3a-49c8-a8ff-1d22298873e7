<mat-dialog-content class="mb-content">
  <form [formGroup]="form" (submit)="update()">
    <div class="row mt-3">
      <div class="col-4">
        <label>
          Location ID
          <input formControlName="id" class="form-control mt-2 input-style-form" type="text" [attr.disabled]="true">
        </label>
      </div>
      <div class="col-4">
        <label>
          Name
          <input formControlName="name" class="form-control mt-2 input-style-form" type="text">
          <mat-error *ngIf="formDialog.name?.errors?.required && formDialog.name.touched">Name is
            required</mat-error>
        </label>
      </div>
      
    </div>

    <div class="row mt-3">
      <div class="col-4">
        <label>
          Latitude
          <input formControlName="gps_latitude" class="form-control mt-2 input-style-form" type="text">
          <mat-error *ngIf="formDialog.gps_latitude?.errors?.required && formDialog.gps_latitude.touched">Latitude is
            required</mat-error>
        </label>
      </div>
      <div class="col-4">
        <label>
          Longitude
          <input formControlName="gps_longitude" class="form-control mt-2 input-style-form" type="text">
          <mat-error *ngIf="formDialog.gps_longitude?.errors?.required && formDialog.gps_longitude.touched">Longitude is
            required</mat-error>
        </label>
      </div>
      <div class="col-4">
        <label>
          Altitude
          <input formControlName="gps_altitude" class="form-control mt-2 input-style-form" type="text">
          <mat-error *ngIf="formDialog.gps_altitude?.errors?.required && formDialog.gps_altitude.touched">Altitude is
            required</mat-error>
        </label>
      </div>
    </div>

    <div class="row mt-3">
     
      <div class="col-4">
        <label>
          Status
          <input formControlName="status" class="form-control mt-2 input-style-form" type="text" [attr.disabled]="true">
          <mat-error *ngIf="formDialog.status?.errors?.required && formDialog.status.touched">Status is
            required</mat-error>
        </label>
      </div>
    </div>

    
    <br>
  </form>
</mat-dialog-content>