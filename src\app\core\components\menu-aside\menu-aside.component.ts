import { ChangeDetectorRef, Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { Observable, Subject, of } from 'rxjs';
import { catchError, map, shareReplay, takeUntil } from 'rxjs/operators';
import { Router } from '@angular/router';
import { from } from 'rxjs';

import { SharedVarService } from '../../../services/SharedVarService/shared-var-service.service';
import { NotificationsService } from '@app/shared/services/notifications.service';
import version from '../../../../../package.json';
import { MatDialog } from '@angular/material/dialog';
import { UserService } from '@app/services/user/user.service';
import { KeycloakService } from 'keycloak-angular';
import { environment } from '@src/environments/environment';


@Component({
  selector: 'app-menu-aside',
  templateUrl: './menu-aside.component.html',
  styleUrls: ['./menu-aside.component.css'],
})
export class MenuAsideComponent implements OnInit, OnDestroy {
  title: string = '';
  letters: string = '';
  user: string = '';
  userRole: number = -1;
  isDetails: boolean = false;
  subject = new Subject<any>();
  version = version.version;
  hideTabs: boolean = false;
  urlIpConnector: string = environment.urlIpConnector;
  urlMicroserviceApp: string = environment.urlMicroserviceApp;
  isCollapsed: boolean = false;

  isHandset$: Observable<boolean> = this.breakpointObserver
    .observe(Breakpoints.Handset)
    .pipe(
      map((result) => result.matches),
      shareReplay()
    );

  constructor(
    private breakpointObserver: BreakpointObserver,
    private sharedVar: SharedVarService,
    private _cdr: ChangeDetectorRef,
    private userService: UserService,
    private keycloakService: KeycloakService,
    private router: Router,
    private notificationsAlerts: NotificationsService,
    public dialog: MatDialog,
  ) {}

  ngOnInit() {
    const savedState = localStorage.getItem('sidebar-collapsed');
    this.isCollapsed = savedState ? JSON.parse(savedState) : false;

    this.sharedVar.getValue().subscribe((value) => {
      this.title = value;
      this.isDetails = !isNaN(parseInt(this.title.slice(-1))) ? true : false;
      this._cdr.detectChanges();
    });

    this.getUserInitials().subscribe((letters) => {
      this.letters = letters;
    });

    this.getUserRoles();
  }

  getCurrentUserProfile(): Observable<any> {
    return from(this.keycloakService.loadUserProfile());
  }

  getUserInitials(): Observable<string> {
    return this.getCurrentUserProfile().pipe(
      map(profile => {
        const firstName = profile.firstName || '';
        const lastName = profile.lastName || '';
        const email = profile.email || '';

        this.user = email;
        this.sharedVar.setStateValue('userEmail', email);

        localStorage.setItem('current_user_email', email);
        localStorage.setItem('current_user_name', `${firstName} ${lastName}`.trim() || profile.username || email);

        if (firstName && lastName) {
          return `${firstName[0]}${lastName[0]}`.toUpperCase();
        } else if (firstName) {
          return firstName[0].toUpperCase();
        } else if (profile.username) {
          return profile.username[0].toUpperCase();
        } else {
          return '';
        }
      })
    );
  }


  getUserAccountConsoleUrl(): string {
    const keycloakInstance = this.keycloakService.getKeycloakInstance();

    return keycloakInstance.createAccountUrl();
  }

  getUserRoles(): void {
    const roles = this.keycloakService.getUserRoles();
    this.sharedVar.setStateValue('userRoles', roles);

    const isAdmin = roles.some(role =>
      role === 'view-users'
    );

    this.sharedVar.setStateValue('isAdmin', isAdmin);
  }

  back(): void {
    this.isDetails ? window.history.back() : false;
  }

  toggleSidebar(): void {
    this.isCollapsed = !this.isCollapsed;
    localStorage.setItem('sidebar-collapsed', JSON.stringify(this.isCollapsed));
    this._cdr.detectChanges();

    setTimeout(() => {
      window.dispatchEvent(new Event('resize'));
    }, 300);
  }

  logout(): void {
    this.router.navigate(['/login']);
  }

  ngOnDestroy(): void {
    this.subject.complete();
    this.subject.unsubscribe();
  }
}
