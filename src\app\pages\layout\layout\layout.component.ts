import { ChangeDetectorRef, Component, HostListener, OnInit } from '@angular/core';
import { SharedVarService } from '@services/SharedVarService/shared-var-service.service';

@Component({
  selector: 'app-layout',
  templateUrl: './layout.component.html',
  styleUrls: ['./layout.component.css']
})
export class LayoutComponent implements OnInit {

  screenWidth: any;
  screenHeight: any;
  title: any = "";
  isCollapsed = false;

  constructor(
    private sharedVar: SharedVarService,
    private _cdr: ChangeDetectorRef
  ) {
    this.getScreenSize();
   }

  @HostListener('window:resize', ['$event'])
  getScreenSize(event?: any){
    const sidebarWidth = this.isCollapsed ? 70 : 250;
    this.screenWidth = window.innerWidth - sidebarWidth - 30;
    this.screenWidth = this.screenWidth + "px";
    this.screenHeight = window.innerHeight - 98;
    this.screenHeight = this.screenHeight + "px";
  }

  ngOnInit() {
    this.sharedVar.getValue().subscribe((value) => {
      this.title = value;
      this._cdr.detectChanges();
    });
  }

  toggleSidebar(): void {
    console.log('LayoutComponent: Toggle sidebar clicked, current state:', this.isCollapsed);
    this.isCollapsed = !this.isCollapsed;
    console.log('LayoutComponent: New state:', this.isCollapsed);
    this.getScreenSize();
    this._cdr.detectChanges();
  }

}
