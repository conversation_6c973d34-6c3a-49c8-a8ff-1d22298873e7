import { Injectable } from '@angular/core';
import { RideTiming } from '@app/core/interfaces/ride-timing.interface';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { catchError, map, switchMap, tap } from 'rxjs/operators';
import { TimeTrackingService } from '@app/services/time-tracking/time-tracking.service';
import { NotificationsService } from '@shared/services/notifications.service';

const RIDE_TIMINGS_STORAGE_KEY = 'ride_timings_history';

@Injectable({
  providedIn: 'root'
})
export class RideTimingService {
  private timers: Record<number, number> = {};
  private running: Record<number, boolean> = {};
  private rideTimings: Record<number, RideTiming[]> = {};
  private rideStartTimestamps: Record<number, string | null> = {};
  private timerIntervals: Record<number, any> = {};
  private timerTimeouts: Record<number, any> = {};
  private activeShiftIds: Record<number, number> = {};

  private timersSubject = new BehaviorSubject<Record<number, number>>({});
  private runningSubject = new BehaviorSubject<Record<number, boolean>>({});
  private rideTimingsSubject = new BehaviorSubject<Record<number, RideTiming[]>>({});

  constructor(
    public timeTrackingService: TimeTrackingService,
    private notificationsService: NotificationsService
  ) {
    this.loadRideTimingsFromStorage();
    this.loadActiveShiftIdsFromStorage();
  }

  private loadRideTimingsFromStorage(): void {
    try {
      const storedTimings = localStorage.getItem(RIDE_TIMINGS_STORAGE_KEY);
      if (storedTimings) {
        this.rideTimings = JSON.parse(storedTimings);
        this.rideTimingsSubject.next(this.rideTimings);
      }
    } catch (error) {
    }
  }

  saveRideTimingsToStorage(): void {
    try {
      localStorage.setItem(RIDE_TIMINGS_STORAGE_KEY, JSON.stringify(this.rideTimings));
    } catch (error) {
    }
  }

  private saveActiveShiftIdsToStorage(): void {
    try {
      localStorage.setItem('activeShiftIds', JSON.stringify(this.activeShiftIds));
    } catch (error) {
    }
  }

  private loadActiveShiftIdsFromStorage(): void {
    try {
      const activeShiftIdsStr = localStorage.getItem('activeShiftIds');
      if (activeShiftIdsStr) {
        this.activeShiftIds = JSON.parse(activeShiftIdsStr);
      }
    } catch (error) {
    }
  }

  getTimers(): Observable<Record<number, number>> {
    return this.timersSubject.asObservable();
  }

  getRunning(): Observable<Record<number, boolean>> {
    return this.runningSubject.asObservable();
  }

  getRideTimings(): Observable<Record<number, RideTiming[]>> {
    return this.rideTimingsSubject.asObservable();
  }

  startTimer(rideId: number, routeId?: number, description?: string): void {
    const userName = localStorage.getItem('current_user_name') || 'Unknown User';
    const userEmail = localStorage.getItem('current_user_email') || '';

    if (!userEmail) {
      this.notificationsService.openSnackBar('User email not found. Cannot start time tracking.', 'Ok');
      return;
    }

    if (!routeId) {
      this.notificationsService.openSnackBar('Route ID is required for time tracking', 'Ok');
      return;
    }

    if (this.running[rideId]) {
      this.notificationsService.openSnackBar('Time tracking already in progress for this ride', 'Ok');
      return;
    }

    for (const id in this.running) {
      if (this.running[id]) {
        this.notificationsService.openSnackBar('Please stop the current time tracking before starting a new one', 'Ok');
        return;
      }
    }

    const timeTrackingDescription = description || `Ride #${rideId}`;

    this.timeTrackingService.getLastShift(userEmail).pipe(
      switchMap(lastShift => {
        console.log('Last shift response:', lastShift);

        if (lastShift && lastShift.id && !lastShift.stop_time) {
          const confirmStop = confirm(`Active time tracking found for route ${lastShift.route_id || 'unknown'}. Do you want to stop it and start a new one?`);

          if (confirmStop) {
            return this.timeTrackingService.stopTimeTracking(lastShift.id).pipe(
              switchMap(() => {
                return this.timeTrackingService.startTimeTracking(
                  routeId,
                  userEmail,
                  timeTrackingDescription,
                  rideId
                );
              }),
              catchError(error => {
                this.notificationsService.openSnackBar('Error stopping previous time tracking', 'Ok');
                return of(null);
              })
            );
          } else {
            this.notificationsService.openSnackBar('Cannot start new time tracking while another is active', 'Ok');
            return of(null);
          }
        }

        // Якщо активного відстеження немає, запускаємо нове
        return this.timeTrackingService.startTimeTracking(
          routeId,
          userEmail,
          timeTrackingDescription,
          rideId
        );
      }),
      catchError(error => {
        console.error('Error checking for active time tracking:', error);

        const errorDetail = error.error?.detail || error.message || 'Unknown error';
        this.notificationsService.openSnackBar(errorDetail, 'Ok');
        return of(null);
      })
    ).pipe(
      catchError(error => {
        console.error('Time tracking error:', error);
        const errorDetail = error.error?.detail || error.message || JSON.stringify(error) || 'Unknown error';
        this.notificationsService.openSnackBar('Error starting time tracking on server: ' + errorDetail, 'Ok');
        return of(null);
      })
    ).subscribe(response => {
      if (!response) return;

      let shiftId = null;
      if (response.id) {
        shiftId = response.id;
      } else if (response.shift_id) {
        shiftId = response.shift_id;
      } else if (response.data?.id) {
        shiftId = response.data.id;
      } else if (response.data?.shift_id) {
        shiftId = response.data.shift_id;
      }

      if (shiftId) {
        this.activeShiftIds[rideId] = shiftId;
        this.saveActiveShiftIdsToStorage();

        const startTimestamp = new Date().toISOString();
        this.rideStartTimestamps[rideId] = startTimestamp;

        this.running[rideId] = true;
        this.runningSubject.next(this.running);

        this.timers[rideId] = 0;
        this.timersSubject.next(this.timers);

        const interval = setInterval(() => {
          this.timers[rideId] = (this.timers[rideId] || 0) + 100;
          this.timersSubject.next(this.timers);
        }, 100);

        this.timerIntervals[rideId] = interval;

        const timeout = setTimeout(() => {
          if (this.running[rideId]) {
            this.stopTimer(rideId, 14400000);
          }
        }, 14400000);

        this.timerTimeouts[rideId] = timeout;

        this.notificationsService.openSnackBar('Time tracking started on server', 'Ok');
      }
    });
  }

  stopTimer(rideId: number, overrideMs?: number, routeId?: number): void {

    try {
      const userName = localStorage.getItem('current_user_name') || 'Unknown User';
      const userEmail = localStorage.getItem('current_user_email') || '';

      if (this.timerIntervals[rideId]) {
        clearInterval(this.timerIntervals[rideId]);
        delete this.timerIntervals[rideId];
      }

      if (this.timerTimeouts[rideId]) {
        clearTimeout(this.timerTimeouts[rideId]);
        delete this.timerTimeouts[rideId];
      }

      this.running[rideId] = false;
      this.runningSubject.next(this.running);

      const finalMs = overrideMs !== undefined ? overrideMs : this.timers[rideId] || 0;
      const startTimestamp = this.rideStartTimestamps[rideId] || new Date().toISOString();
      const endTimestamp = new Date().toISOString();

      if (finalMs >= 100) {
        const newTiming: RideTiming = {
          ms: finalMs,
          name: userName,
          startTimestamp,
          endTimestamp
        };

        if (!this.rideTimings[rideId]) {
          this.rideTimings[rideId] = [];
        }
        this.rideTimings[rideId] = [newTiming, ...this.rideTimings[rideId]];
        this.rideTimingsSubject.next(this.rideTimings);

        this.saveRideTimingsToStorage();


        if (!userEmail) {
          this.notificationsService.openSnackBar('User email not found. Cannot stop time tracking.', 'Ok');
          return;
        }
        this.timeTrackingService.getLastShift(userEmail).pipe(
          catchError((error: any) => {
            const errorDetail = error.error?.detail || error.message || 'Error getting last shift';
            this.notificationsService.openSnackBar(errorDetail, 'Ok');
            return of(null);
          })
        ).subscribe(lastShift => {
          if (lastShift && lastShift.id) {
            const shiftId = lastShift.id;
            this.timeTrackingService.stopTimeTracking(
              shiftId
            ).pipe(
              catchError((error: any) => {
                const errorDetail = error.error?.detail || error.message || 'Error stopping time tracking on server';
                this.notificationsService.openSnackBar(errorDetail, 'Ok');
                return of(null);
              })
            ).subscribe(response => {
              if (response) {
                this.notificationsService.openSnackBar('Time tracking stopped on server', 'Ok');
                delete this.activeShiftIds[rideId];
                this.saveActiveShiftIdsToStorage();
              }
            });
          } else {
            this.notificationsService.openSnackBar('No active shift found', 'Ok');
          }
        });
      }

      this.timers[rideId] = 0;
      this.timersSubject.next(this.timers);

      this.rideStartTimestamps[rideId] = null;
    } catch (error) {
      // Error handled silently
    }
  }

  resetTimer(rideId: number): void {
    if (this.running[rideId]) {
      this.stopTimer(rideId);
    }


    this.timers[rideId] = 0;
    this.timersSubject.next(this.timers);
  }

  updateTiming(rideId: number, idx: number, newMs: number, routeId?: number): void {
    try {
      if (!this.rideTimings[rideId] || !this.rideTimings[rideId][idx]) {
        return;
      }

      const userEmail = localStorage.getItem('current_user_email') || '';
      const timing = this.rideTimings[rideId][idx];
      const startTime = timing.startTimestamp;

      this.rideTimings[rideId][idx] = {
        ...this.rideTimings[rideId][idx],
        ms: newMs,
        edited: true
      };

      this.rideTimingsSubject.next(this.rideTimings);
      this.saveRideTimingsToStorage();

      if (timing.fromServer && timing.shiftId) {
        const startDate = new Date(startTime);
        const endDate = new Date(startDate.getTime() + newMs);

        this.timeTrackingService.editTimeTracking(
          timing.shiftId,
          {
            stop_time: endDate.toISOString()
          }
        ).pipe(
          catchError((error: any) => {
            this.notificationsService.openSnackBar('Error updating time tracking on server', 'Ok');
            return of(null);
          })
        ).subscribe(response => {
          if (response) {
            this.notificationsService.openSnackBar('Time tracking updated on server', 'Ok');

            this.rideTimings[rideId][idx].endTimestamp = endDate.toISOString();
            this.rideTimingsSubject.next(this.rideTimings);
            this.saveRideTimingsToStorage();
          }
        });
      } else {
        const startDate = new Date(startTime);
        const endDate = new Date(startDate.getTime() + newMs);
        this.rideTimings[rideId][idx].endTimestamp = endDate.toISOString();
        this.rideTimingsSubject.next(this.rideTimings);
        this.saveRideTimingsToStorage();
      }
    } catch (error) {
      this.notificationsService.openSnackBar('Error updating timing', 'Ok');
    }
  }

  getFormattedTime(rideId: number): string {
    const ms = this.timers[rideId] || 0;
    return this.formatMilliseconds(ms);
  }

  formatMilliseconds(ms: number): string {
    const hours = Math.floor(ms / 3600000).toString().padStart(2, '0');
    const minutes = Math.floor((ms % 3600000) / 60000).toString().padStart(2, '0');
    const seconds = Math.floor((ms % 60000) / 1000).toString().padStart(2, '0');
    const tenths = Math.floor((ms % 1000) / 100);

    return `${hours}:${minutes}:${seconds},${tenths}`;
  }

  formatTimestamp(timestamp: string): string {
    if (!timestamp) return '';

    const date = new Date(timestamp);
    return `${date.getFullYear()}-${String(date.getMonth()+1).padStart(2,'0')}-${String(date.getDate()).padStart(2,'0')} ${String(date.getHours()).padStart(2,'0')}:${String(date.getMinutes()).padStart(2,'0')}`;
  }

  formatTimestampToTime(timestamp: string): string {
    if (!timestamp) return '';

    const date = new Date(timestamp);
    return `${String(date.getHours()).padStart(2,'0')}:${String(date.getMinutes()).padStart(2,'0')}`;
  }

  isTimerRunning(rideId: number): boolean {
    return this.running[rideId] || false;
  }

  getTimingsForRide(rideId: number): RideTiming[] {
    return this.rideTimings[rideId] || [];
  }

  loadServerTimings(rideId: number): void {

    const userEmail = localStorage.getItem('current_user_email') || '';

    if (!userEmail || !rideId) return;

    this.timeTrackingService.getShiftEvents(
      undefined, undefined, userEmail, undefined, rideId
    ).pipe(
      catchError((error: any) => {
        return of({ data: [] });
      })
    ).subscribe(response => {
      if (response && response.data && Array.isArray(response.data) && response.data.length > 0) {
        const serverTimings: RideTiming[] = (response.data as any[]).map((shift: any) => {
          const startTime = new Date(shift.start_time);
          const stopTime = shift.stop_time ? new Date(shift.stop_time) : new Date();
          const ms = stopTime.getTime() - startTime.getTime();

          return {
            ms,
            name: shift.pilot_email,
            startTimestamp: shift.start_time,
            endTimestamp: shift.stop_time || new Date().toISOString(),
            fromServer: true,
            shiftId: shift.id,
            description: shift.description
          };
        });

        if (!this.rideTimings[rideId]) {
          this.rideTimings[rideId] = [];
        }


        const existingStartTimes = new Set(
          this.rideTimings[rideId]
            .filter(t => t.fromServer)
            .map(t => t.startTimestamp)
        );

        const newServerTimings = serverTimings.filter(
          t => !existingStartTimes.has(t.startTimestamp)
        );

        if (newServerTimings.length > 0) {
          this.rideTimings[rideId] = [...newServerTimings, ...this.rideTimings[rideId]];
          this.rideTimingsSubject.next(this.rideTimings);
          this.saveRideTimingsToStorage();
        }
      }
    });
  }

  getTotalTimeForRide(rideId: number): number {
    const timings = this.rideTimings[rideId] || [];
    return timings.reduce((total, timing) => total + timing.ms, 0);
  }

  getFormattedTotalTimeForRide(rideId: number): string {
    const totalMs = this.getTotalTimeForRide(rideId);
    return this.formatMilliseconds(totalMs);
  }
}
