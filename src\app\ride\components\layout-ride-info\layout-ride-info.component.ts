import { Component, OnInit, Inject } from '@angular/core';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Ride } from '@app/core/interfaces/ride.interface';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-layout-ride-info',
  templateUrl: './layout-ride-info.component.html',
  styleUrls: ['./layout-ride-info.component.css'],
})
export class LayoutRideInfoComponent implements OnInit {
  flagGeneral = true;
  flagDeliveries = true;
  subject = new Subject<any>();

  constructor(@Inject(MAT_DIALOG_DATA) public data: Ride) {}

  ngOnInit(): void {
    this.subject.next(this.data);
  }

  eventFlagGeneral(e: any): void {
    this.flagGeneral = e;
  }

  eventFlagDeliveries(e: any): void {
    this.flagDeliveries = e;
  }
}
