import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DialogRideOperatorComponent } from './dialog-ride-operator.component';
import {
  MatDialogModule,
  MAT_DIALOG_DATA,
  MatDialogRef,
} from '@angular/material/dialog';
import { NotificationsService } from '@app/shared/services/notifications.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { RideService } from '@app/services/ride/ride.service';

describe('DialogRideOperatorComponent', () => {
  let component: DialogRideOperatorComponent;
  let fixture: ComponentFixture<DialogRideOperatorComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DialogRideOperatorComponent],
      imports: [MatDialogModule, HttpClientTestingModule, MatSnackBarModule],
      providers: [
        {
          provide: MAT_DIALOG_DATA,
          useValue: {},
        },
        {
          provide: MatDialogRef,
          useValue: {
            close: jasmine.createSpy(),
          },
        },
        RideService,
        NotificationsService,
        MatSnackBar,
      ],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DialogRideOperatorComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
