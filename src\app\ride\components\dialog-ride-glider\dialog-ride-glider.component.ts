import { Component, OnInit, Inject } from '@angular/core';
import { Subject, of, Observable } from 'rxjs';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { catchError, map, startWith, takeUntil } from 'rxjs/operators';

import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { NotificationsService } from '@shared/services/notifications.service';

import { RideService } from '@app/services/ride/ride.service';
import { Ride } from '@app/core/interfaces/ride.interface';
import { Glider } from '@app/core/interfaces/glider.interace';
import { FleetService } from '@app/services/fleet/fleet.service';
import { typeAction } from '@app/shared/utils/enum';

@Component({
  selector: 'app-dialog-ride-glider',
  templateUrl: './dialog-ride-glider.component.html',
  styleUrls: ['./dialog-ride-glider.component.css'],
})
export class DialogRideGliderComponent implements OnInit {
  form!: FormGroup;
  subject = new Subject<any>();
  filteredOptions: Observable<Glider[]>;

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: Ride,
    @Inject(MAT_DIALOG_DATA) public dataGlider: Glider[],
    private rideService: RideService,
    private fleetService: FleetService,
    private notificationsAlerts: NotificationsService,
    public dialogRef: MatDialogRef<DialogRideGliderComponent>
  ) {}

  ngOnInit(): void {
    this.form = new FormGroup({
      ride_id: new FormControl(this.data.id, [Validators.required]),
      glider: new FormControl(this.data.glider_name, [Validators.required]),
    });

    this.loadData();
  }

  loadData(): void {
      this.fleetService
      .getFleetList()
      .pipe(
        takeUntil(this.subject),
        catchError((err) => {
          this.notificationsAlerts.openSnackBar('Error', 'Ok');
          return of(err);
        })
      )
      .subscribe((res) => {
        this.dataGlider = res;

        this.filteredOptions = this.form.controls.glider.valueChanges.pipe(
          startWith(''),
          map((value) => {
            const note =
              typeof value === 'string' ? value : value?.name || value?.id;
            return note
              ? this._filter(note as string)
              : this.dataGlider?.slice();
          })
        );
      });
  }

  displayFn(glider: Glider): string {
    return glider && glider.name ? glider.name : '';
  }

  private _filter(value: string): Glider[] {
    const filterValue = value.toLowerCase();

    return this.dataGlider.filter(
      (glider) =>
        glider.name.toLowerCase().includes(filterValue) ||
        glider.id.toString().includes(filterValue)
    );
  }

  accept(): void {
    this.dialogRef.close(true);
  }

  cancel(): void {
    this.dialogRef.close(false);
  }

  update(): void {
    if (!this.form.invalid && this.form.value.glider.id) {
      const body = {
        operator_id: null,
        ride_status_id: null,
        has_package: null,
        package_description: null,
        departure_time: null,
        arrival_time: null,
        glider_id: this.form.value.glider.id,
        glider_name: this.form.value.glider.name,
    };

      this.rideService
        .updateRide(this.data.id, body)
        .pipe(
          catchError((err) => {
            this.notificationsAlerts.openSnackBar('Error', 'Ok');
            return of(err);
          })
        )
        .subscribe((res) => {
          if (res.id) {
            this.rideService.notifyDataSubject(res, typeAction.updated);
            this.dialogRef.close();
          }
          this.notificationsAlerts.openSnackBar("Assigned to glider", 'Ok');
        });
    }
  }

  get formDialog(): any {
    return this.form.controls;
  }

  ngOnDestroy(): void {
    this.subject.complete();
    this.subject.unsubscribe();
  }
}
