import {Component, Inject} from '@angular/core';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';

export interface ConfirmSoftwareVersionData {
  oldVersion: string;
  newVersion: string;
}

@Component({
  selector: 'app-confirm-software-version-dialog',
  template: `
    <h2 mat-dialog-title>Confirm Version Change</h2>
    <mat-dialog-content>
      <p>
        Are you sure you want to change from
        "<strong>{{ data.oldVersion }}</strong>"
        to "<strong>{{ data.newVersion }}</strong>"?
      </p>
    </mat-dialog-content>
    <mat-dialog-actions align="end">
      <button mat-button (click)="onCancel()">Cancel</button>
      <button mat-raised-button color="primary" (click)="onConfirm()">OK</button>
    </mat-dialog-actions>
  `
})
export class ConfirmSoftwareVersionDialogComponent {
  constructor(
    public dialogRef: MatDialogRef<ConfirmSoftwareVersionDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmSoftwareVersionData
  ) {
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }

  onConfirm(): void {
    this.dialogRef.close(true);
  }
}
