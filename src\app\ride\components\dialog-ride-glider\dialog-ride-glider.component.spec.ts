import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DialogRideGliderComponent } from './dialog-ride-glider.component';
import {
  MatDialogModule,
  MAT_DIALOG_DATA,
  MatDialogRef,
} from '@angular/material/dialog';
import { NotificationsService } from '@app/shared/services/notifications.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { RideService } from '@app/services/ride/ride.service';

describe('DialogRideGliderComponent', () => {
  let component: DialogRideGliderComponent;
  let fixture: ComponentFixture<DialogRideGliderComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DialogRideGliderComponent],
      imports: [MatDialogModule, HttpClientTestingModule, MatSnackBarModule],
      providers: [
        {
          provide: MAT_DIALOG_DATA,
          useValue: {},
        },
        {
          provide: MatDialogRef,
          useValue: {
            close: jasmine.createSpy(),
          },
        },
        RideService,
        NotificationsService,
        MatSnackBar,
      ],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DialogRideGliderComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
