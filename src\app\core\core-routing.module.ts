import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { MenuAsideComponent } from './components/menu-aside/menu-aside.component';

const routes: Routes = [
  {
    path: '',
    component: MenuAsideComponent,
    children: [
      {
        path: '',
        redirectTo: 'users',
        pathMatch: 'full',
      },
      {
        path: 'locations',
        loadChildren: () =>
          import('@app/location/location.module').then((m) => m.LocationModule),
      },
      {
        path: 'fleets',
        loadChildren: () =>
          import('../fleet/fleet.module').then((m) => m.FleetModule),
      },
      {
        path: 'maintenances',
        loadChildren: () =>
          import('../maintenance/maintenance.module').then(
            (m) => m.MaintenanceModule
          ),
      },
      {
        path: 'rides',
        loadChildren: () =>
          import('../ride/ride.module').then((m) => m.RideModule),
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class CoreRoutingModule {}
