import {Injectable} from '@angular/core';
import {HttpClient, HttpParams} from '@angular/common/http';
import {Observable} from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class FlightReviewService {
  private BROWSE_URL = 'https://flight-review.uphi.ch/api/browse';
  private INCIDENT_URL = 'https://flight-review.uphi.ch/api/incident';

  constructor(private http: HttpClient) {
  }

  getFlightsByGlider(
    gliderName: string,
    // pageNo: number = 0,
    // pageSize: number = 10,
    // orderColumn: string = 'start_time',
    // orderDir: string = 'desc'
  ): Observable<any> {
    let params = new HttpParams();
      // .set('page_no', pageNo)
      // .set('page_size', pageSize)
      // .set('order_column', orderColumn)
      // .set('order_dir', orderDir);

    if (gliderName) {
      params = params.set('glider', gliderName);
    }

    return this.http.get(this.BROWSE_URL, {params});
  }

  getLastFlightsByGlider(gliderName: string, count: number = 5): Observable<any> {
    let params = new HttpParams();
      // .set('page_size', count)
      // .set('order_column', 'start_time')
      // .set('order_dir', 'desc');

    if (gliderName) {
      params = params.set('glider', gliderName);
    }

    return this.http.get(this.BROWSE_URL, {params});
  }

  getNextFlightsByGlider(gliderName: string, count: number = 5): Observable<any> {
    const todayString = new Date().toISOString().slice(0, 10);
    let params = new HttpParams()
      .set('page_size', count)
      .set('order_column', 'start_time')
      .set('order_dir', 'asc')
      .set('flight_date_start', todayString);

    if (gliderName) {
      params = params.set('glider', gliderName);
    }

    return this.http.get(this.BROWSE_URL, {params});
  }

  getLastIncidents(): Observable<any> {
    const params = new HttpParams();
      // .set('page_size', count);
    return this.http.get(this.INCIDENT_URL, {params});
  }
}
