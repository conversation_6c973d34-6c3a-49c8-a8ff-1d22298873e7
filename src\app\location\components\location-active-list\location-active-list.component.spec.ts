import { ComponentFixture, TestBed } from '@angular/core/testing';

import { LocationActiveListComponent } from './location-active-list.component';
import { LocationService } from '@app/services/location/location.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { MatDialogModule } from '@angular/material/dialog';
import { MatSnackBarModule } from '@angular/material/snack-bar';

describe('LocationActiveListComponent', () => {
  let component: LocationActiveListComponent;
  let fixture: ComponentFixture<LocationActiveListComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [LocationActiveListComponent],
      providers: [LocationService],
      imports: [
        HttpClientTestingModule,
        NoopAnimationsModule,
        MatDialogModule,
        MatSnackBarModule,
      ],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(LocationActiveListComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
