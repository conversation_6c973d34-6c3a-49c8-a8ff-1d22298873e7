import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class SidebarService {
  private readonly STORAGE_KEY = 'sidebar-collapsed';
  private collapsedSubject = new BehaviorSubject<boolean>(this.getInitialState());

  constructor() {}

  private getInitialState(): boolean {
    const stored = localStorage.getItem(this.STORAGE_KEY);
    return stored ? JSON.parse(stored) : false;
  }

  get isCollapsed$(): Observable<boolean> {
    return this.collapsedSubject.asObservable();
  }

  get isCollapsed(): boolean {
    return this.collapsedSubject.value;
  }

  toggle(): void {
    const newState = !this.collapsedSubject.value;
    this.collapsedSubject.next(newState);
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(newState));
  }

  collapse(): void {
    if (!this.isCollapsed) {
      this.toggle();
    }
  }

  expand(): void {
    if (this.isCollapsed) {
      this.toggle();
    }
  }
}
