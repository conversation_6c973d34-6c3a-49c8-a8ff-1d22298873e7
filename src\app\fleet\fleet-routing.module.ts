import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { FleetDetailsComponent } from './components/fleet-details/fleet-details.component';
import { FleetListComponent } from './components/fleet-list/fleet-list.component';

const routes: Routes = [
  {
    path: '',
    component: FleetListComponent,
  },
  {
    path: 'fleet-details/:id',
    component: FleetDetailsComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class FleetRoutingModule {}
