import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { LayoutRoutingModule } from './layout-routing.module';
import { LayoutComponent } from '../layout/layout/layout.component';
import { AsideMenuComponent } from './components/aside-menu/aside-menu.component';
import { InlineSVGModule } from 'ng-inline-svg';
import { MaterialModule } from '../../material/material.module';
import { SharedModule } from '../../shared/shared.module';

@NgModule({
  declarations: [LayoutComponent, AsideMenuComponent],
  imports: [
    CommonModule,
    LayoutRoutingModule,
    SharedModule,
    InlineSVGModule,
    MaterialModule
  ]
})
export class LayoutModule { }
