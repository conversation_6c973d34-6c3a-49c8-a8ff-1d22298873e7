import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DialogLocationEditComponent } from './dialog-location-edit.component';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { LocationService } from '@app/services/location/location.service';
import { AuthModule } from '@auth/auth.module';

describe('DialogLocationEditComponent', () => {
  let component: DialogLocationEditComponent;
  let fixture: ComponentFixture<DialogLocationEditComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DialogLocationEditComponent],
      providers: [
        {
          provide: MAT_DIALOG_DATA,
          useValue: {},
        },
        {
          provide: MatDialogRef,
          useValue: {},
        },
        LocationService,
      ],
      imports: [AuthModule],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DialogLocationEditComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
