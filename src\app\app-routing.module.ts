import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AuthGuard } from './auth/auth.guard';
// import { LoginGuard } from './auth/guards/login.guard';

const routes: Routes = [
  {
    path: '',
    loadChildren: () =>
      import('./core/core.module').then((m) => m.CoreModule)
  },
  { path: '**', redirectTo: '', pathMatch: 'full' },
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
