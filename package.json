{"name": "delivery-glider", "version": "0.0.14", "scripts": {"ng": "ng", "start": "ng serve", "start proxy": "ng serve --proxy-config proxy.conf.json", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@agm/core": "^3.0.0-beta.0", "@angular/animations": "~12.0.1", "@angular/cdk": "^12.0.1", "@angular/common": "~12.0.1", "@angular/compiler": "~12.0.1", "@angular/core": "~12.0.1", "@angular/forms": "~12.0.1", "@angular/google-maps": "^12.0.2", "@angular/localize": "~12.0.1", "@angular/material": "~12.0.1", "@angular/platform-browser": "~12.0.1", "@angular/platform-browser-dynamic": "~12.0.1", "@angular/router": "~12.0.1", "@ngx-env/builder": "^15.1.0", "bootstrap": "^5.0.1", "google-libphonenumber": "^3.2.29", "intl-tel-input": "^17.0.3", "keycloak-angular": "^8.4.0", "keycloak-js": "^15.1.1", "moment": "^2.29.3", "ng-inline-svg": "^13.0.0", "ng-magnizoom": "^1.0.0", "ngx-bootstrap": "^8.0.0", "ngx-intl-tel-input": "^3.2.0", "postcss": "^8.2.15", "rxjs": "~6.6.0", "stylelint-config-standard": "^29.0.0", "tslib": "^2.0.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "^12.0.1", "@angular/cli": "~12.0.1", "@angular/compiler-cli": "^12.0.1", "@types/googlemaps": "^3.39.12", "@types/jasmine": "~3.6.0", "@types/node": "^12.11.1", "codelyzer": "^6.0.0", "jasmine-core": "~3.6.0", "jasmine-spec-reporter": "~5.0.0", "karma": "^6.3.2", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.0.3", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "protractor": "~7.0.0", "ts-node": "~8.3.0", "tslint": "~6.1.0", "typescript": "~4.2.4"}}