import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DialogGliderInfoComponent } from './dialog-glider-information.component';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { AuthModule } from '@auth/auth.module';
import { RideService } from '@app/services/ride/ride.service';

describe('DialogGliderInfoComponent', () => {
  let component: DialogGliderInfoComponent;
  let fixture: ComponentFixture<DialogGliderInfoComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DialogGliderInfoComponent],
      providers: [
        {
          provide: MAT_DIALOG_DATA,
          useValue: {},
        },
        {
          provide: MatDialogRef,
          useValue: {},
        },
        RideService,
      ],
      imports: [AuthModule],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DialogGliderInfoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
