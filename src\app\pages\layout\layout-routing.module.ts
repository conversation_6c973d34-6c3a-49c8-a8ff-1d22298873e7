import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { LayoutComponent } from '../layout/layout/layout.component';
import { LoginComponent } from '@auth/components/login/login.component';

import { AuthGuard } from '@auth/guards/auth.guard';
import { LoginGuard } from '@auth/guards/login.guard';

const routes: Routes = [
  {
    path: '',
    component: LayoutComponent,
    children: [
      {
        path: '',
        redirectTo: 'locations',
        pathMatch: 'full',
      },
      {
        path: 'users',
        loadChildren: () =>
          import('@user/user.module').then((m) => m.UserModule),
        // canActivate: [AuthGuard]
      },
      {
        path: 'nodes',
        loadChildren: () =>
          import('@node/node.module').then((m) => m.NodeModule),
        // canActivate: [AuthGuard]
      },
      {
        path: 'fleets',
        loadChildren: () =>
          import('@fleet/fleet.module').then((m) => m.FleetModule),
        // canActivate: [AuthGuard]
      },
      {
        path: 'rides',
        loadChildren: () =>
          import('@ride/ride.module').then((m) => m.RideModule),
        // canActivate: [AuthGuard]
      },
      {
        path: 'locations',
        loadChildren: () =>
          import('@app/location/location.module').then((m) => m.LocationModule),
        // canActivate: [AuthGuard]
      },
      {
        path: 'deliveries',
        loadChildren: () =>
          import('@delivery/delivery.module').then((m) => m.DeliveryModule),
        // canActivate: [AuthGuard]
      },
      {
        path: 'jedsetters',
        loadChildren: () =>
          import('@jedsetter/jedsetter.module').then((m) => m.JedsetterModule),
        // canActivate: [AuthGuard]
      },
      {
        path: 'tasks',
        loadChildren: () =>
          import('@task/task.module').then((m) => m.TaskModule),
        // canActivate: [AuthGuard]
      },
      {
        path: 'tickets',
        loadChildren: () =>
          import('@ticket/ticket.module').then((m) => m.TicketModule),
        // canActivate: [AuthGuard]
      },
      {
        path: 'notifications',
        loadChildren: () =>
          import('@notification/notification.module').then(
            (m) => m.NotificationModule
          ),
        // canActivate: [AuthGuard]
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class LayoutRoutingModule {}
