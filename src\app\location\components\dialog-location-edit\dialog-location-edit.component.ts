import { Component, OnInit, Inject, OnD<PERSON>roy } from '@angular/core';
import { Subject, of, Observable } from 'rxjs';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { catchError, map, startWith, takeUntil } from 'rxjs/operators';

import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import {
  Location,
  LocationCategory,
} from '@app/core/interfaces/location.interface';
import { LocationService } from '@app/services/location/location.service';
import { NotificationsService } from '@shared/services/notifications.service';

import { typeAction } from '@app/shared/utils/enum';
import { environment } from '@src/environments/environment';
import { UserData } from '@app/core/interfaces/userdata.interface';

@Component({
  selector: 'app-dialog-location-edit',
  templateUrl: './dialog-location-edit.component.html',
  styleUrls: ['./dialog-location-edit.component.css'],
})
export class DialogLocationEditComponent implements OnInit, OnDestroy {
  form!: FormGroup;
  subject = new Subject<any>();
  public edit = false;
  videoUrl: string;
  filteredOptionsOwner: Observable<UserData[]>;
  filteredOptionsNode: Observable<Node[]>;
  dataCategory: LocationCategory[] = [];
  dataOwner: UserData[] = [];
  dataNode: Node[] = [];

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: Location,
    private locationService: LocationService,
    private notificationsAlerts: NotificationsService,
    public dialogRef: MatDialogRef<DialogLocationEditComponent>
  ) {
    this.form = new FormGroup({
      id: new FormControl({value: this.data.id, disabled: true}),
      name: new FormControl({value: this.data.name, disabled: true}),
      category_id: new FormControl(this.data.locationCategory.id, [
        Validators.required,
      ]),
      gps_latitude: new FormControl({value: this.data.gpsLat, disabled: true}),
      gps_longitude: new FormControl({value: this.data.gpsLong, disabled: true}),
      gps_altitude: new FormControl({value: this.data.gpsAlt, disabled: true}),
      status: new FormControl({value: this.data.locationStatus.name, disabled: true}),
    });

    // this.videoUrl = `${environment.url_video}${this.data.video_url}`;
  }

  ngOnInit(): void {
    this.loadData();
  }

  loadData(): void {
    this.locationService
      .getLocationsCategoriesList()
      .pipe(
        takeUntil(this.subject),
        catchError((err) => {
          this.notificationsAlerts.openSnackBar('Error', 'Ok');
          return of(err);
        })
      )
      .subscribe((res) => {
        this.dataCategory = res;
      });

    // this.userService
    //   .getUserList()
    //   .pipe(
    //     takeUntil(this.subject),
    //     catchError((err) => {
    //       this.notificationsAlerts.openSnackBar('Error', 'Ok');
    //       return of(err);
    //     })
    //   )
    //   .subscribe((res) => {
    //     this.dataOwner = res.data;

    //     this.filteredOptionsOwner = this.form.controls.owner.valueChanges.pipe(
    //       startWith(''),
    //       map((value) => {
    //         const note =
    //           typeof value === 'string'
    //             ? value
    //             : value?.company_name || value?.first_name || value?.id;
    //         return note
    //           ? this._filterOwner(note as string)
    //           : this.dataOwner.slice();
    //       })
    //     );
    //   });

    // this.nodeService
    //   .getNodeList()
    //   .pipe(
    //     takeUntil(this.subject),
    //     catchError((err) => {
    //       this.notificationsAlerts.openSnackBar('Error', 'Ok');
    //       return of(err);
    //     })
    //   )
    //   .subscribe((res) => {
    //     this.dataNode = res.data;

    //     this.filteredOptionsNode = this.form.controls.node.valueChanges.pipe(
    //       startWith(''),
    //       map((value) => {
    //         const note =
    //           typeof value === 'string' ? value : value?.name || value?.id;
    //         return note
    //           ? this._filterNode(note as string)
    //           : this.dataNode.slice();
    //       })
    //     );
    //   });
  }

  displayFnOwner(user: UserData): string {
    return user && user.first_name && user.last_name && !user.company_name
      ? user.first_name + ' ' + user.last_name
      : user.company_name!!;
  }

  displayFnNode(node: Node): string {
    return '';
  }

  private _filterOwner(value: string): UserData[] {
    const filterValue = value.toLowerCase();

    return this.dataOwner.filter(
      (user) =>
        user.company_name?.toLowerCase().includes(filterValue) ||
        user.first_name?.toLowerCase().includes(filterValue) ||
        user.id.toString().includes(filterValue)
    );
  }

  private _filterNode(value: string): Node[] {
    const filterValue = value.toLowerCase();

    return this.dataNode.filter(
      (node) =>
        true
    );
  }

  update(): void {
    if (!this.form.invalid) {
      const body = {
        location: {
          name: this.form.value.name,
          location_category_id: this.form.value.category_id,
          gps_latitude: this.form.value.gps_latitude,
          gps_longitude: this.form.value.gps_longitude,
          gps_altitude: this.form.value.gps_altitude,
          heading: this.form.value.heading,
          owner_id: this.form.value.owner.id,
          location_status_id: this.form.value.location_status_id,
          ready_eta: this.form.value.ready_eta,
          node_id: this.form.value.node.id,
          tag: this.form.value.tag,
        },
      };
      this.locationService
        .updateLocation(this.data.id, body)
        .pipe(
          takeUntil(this.subject),
          catchError((err) => {
            this.notificationsAlerts.openSnackBar('Error', 'Ok');
            return of(err);
          })
        )
        .subscribe((res) => {
          if (res.status) {
            this.locationService.notifyDataSubject(
              res.data,
              typeAction.updated
            );
            this.dialogRef.close();
          }
          this.notificationsAlerts.openSnackBar(res.message, 'Ok');
        });
    }
  }

  get formDialog(): any {
    return this.form.controls;
  }

  ngOnDestroy(): void {
    this.subject.complete();
    this.subject.unsubscribe();
  }
}
