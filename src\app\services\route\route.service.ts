import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from '@src/environments/environment';
import { KeycloakService } from 'keycloak-angular';
import { Observable } from 'rxjs';
import { from } from 'rxjs';
import { switchMap } from 'rxjs/operators';
import { Route } from '@app/core/interfaces/ride.interface';

@Injectable({
  providedIn: 'root'
})
export class RouteService {
  private apiUrl = environment.urlMsRides;

  constructor(private http: HttpClient, private keycloakService: KeycloakService) { }

  getRouteById(routeId: number): Observable<Route> {
    return from(this.keycloakService.getToken()).pipe(
      switchMap((token) => {
        const headers = new HttpHeaders({
          Authorization: `Bearer ${token}`,
        });
        return this.http.get<Route>(`${this.apiUrl}/routes/${routeId}`, { headers });
      })
    );
  }

  getRoutesList(): Observable<Route[]> {
    return from(this.keycloakService.getToken()).pipe(
      switchMap((token) => {
        const headers = new HttpHeaders({
          Authorization: `Bearer ${token}`,
        });
        return this.http.get<Route[]>(`${this.apiUrl}/routes`, { headers });
      })
    );
  }
}
