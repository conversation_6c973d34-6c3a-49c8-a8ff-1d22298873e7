import { Glider } from "@app/core/interfaces/glider.interace";

export const GLIDER_TASKS: Glider =
{
  id: 1,
  name: "<PERSON><PERSON>",
  ip: "**************",
  vpn: "************",
  glider_status: {
    id: 4,
    status: "offline",
    comment: null,
    created_at: "2022-05-02T14:30:20.692Z",
    updated_at: "2022-05-02T14:30:20.692Z"
  },
  ready_eta: "2022-05-02T00:00:00.000Z",
  landing_eta: "2022-05-02T00:00:00.000Z",
  battery: 98,
  gps_longitude: 50359383,
  gps_latitude: 89902422,
  gps_altitude: 451,
  gps_time: 1651501825,
  created_at: "2022-05-02T14:30:25.800Z",
  updated_at: "2022-05-02T14:30:25.800Z",
}