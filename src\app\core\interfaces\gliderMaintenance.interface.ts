import { Glider } from './glider.interace';

export interface maintenanceType {
  id: number;
  name: string;
}

export interface GliderMaintenance {
  postMaintenanceChecklistDone: boolean;
  id: number;
  dueDate: string;
  completedDate: string;
  isScheduled: boolean;
  notes: string;
  glider: Glider;
  maintenanceType: maintenanceType;
  maintenanceTypeId?: number;
  gliderId?: number;
  mailboxId?: number;
}

export interface MaintenanceType {
  id: number;
  name: string;
}

export interface Mailbox {
  id: number;
  createdAt: string;
  updatedAt: string;
  name: string;
}

export interface GliderMaintenance {
  id: number;
  dueDate: string;
  completedDate: string;
  isScheduled: boolean;
  notes: string;
  maintenanceStaff: string;
  postMaintenanceChecklistDone: boolean;
  glider: Glider;
  mailbox: Mailbox;
  maintenanceType: MaintenanceType;
}
