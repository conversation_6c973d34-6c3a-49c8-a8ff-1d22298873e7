import { <PERSON><PERSON><PERSON> } from './glider.interace';
import { Location } from './location.interface';
import { UserData } from './userdata.interface';

export interface Route {
  start_location_id: number;
  start_location_name: string;
  end_location_id: number;
  end_location_name: string;
  emergency_contact: string;
  known_dangers: string;
  extra_notes: string;
  customer_id: number;
  id: number;
  customer?: {
    name: string;
    id: number;
  };
}

export interface RideStatus {
  id: number;
  name: string;
  description: string;
}

export interface Ride {
  cancel_reason: string;
  from_location: number;
  to_location: number;
  from_location_name?: string;
  to_location_name?: string;
  operator_email?: string;
  ride_status_description?: string;
  departure_time: string;
  arrival_time: string;
  ride_status_id: number;
  ride_status?: string | RideStatus;
  updating?: boolean;
  glider_id: number;
  glider_name: string;
  glider_status?: any;
  operator_id: string;
  id: number;
  category?: string;
  gsc_id?: string;
  scheduled?: string;
  has_package?: boolean;
  package_description?: string;
  delivered_by_car?: boolean;
  route_id?: number;
  route?: Route;
}

export interface RideExportFilters {
  start_date?: string,
  end_date?: string,
  glider?: Glider,
  start_location?: Location,
  destination_location?: Location,
  include_package: boolean,
  include_no_package: boolean
}



interface RideCategory {
  id: number;
  category: string;
  comment?: string | null;
  created_at: string;
  updated_at: string;
}

interface RideControlStation {
  id: number;
  gcs_ip: string;
  created_at: string;
  updated_at: string;
}
