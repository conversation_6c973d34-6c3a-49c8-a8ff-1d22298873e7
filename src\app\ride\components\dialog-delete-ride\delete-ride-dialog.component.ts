import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Ride } from '@app/core/interfaces/ride.interface';

@Component({
  selector: 'app-delete-ride-dialog',
  template: `
    <h2 mat-dialog-title>Delete Ride</h2>
    <mat-dialog-content>
      <p>Are you sure you want to delete this ride?</p>
      <div class="ride-details">
        <p><strong>Departure:</strong> {{ data.from_location_name }}</p>
        <p><strong>Arrival:</strong> {{ data.to_location_name }}</p>
        <p><strong>Departure Time:</strong> {{ data.departure_time | date:'medium' }}</p>
        <p><strong>Status:</strong> {{ data.ride_status }}</p>
      </div>
      <p class="warning">This action cannot be undone.</p>
    </mat-dialog-content>
    <mat-dialog-actions align="end">
      <button mat-button (click)="onCancel()">Cancel</button>
      <button mat-raised-button color="warn" (click)="onConfirm()">Delete</button>
    </mat-dialog-actions>
  `,
  styles: [`
    .ride-details {
      margin: 16px 0;
      padding: 12px;
      background-color: #f5f5f5;
      border-radius: 4px;
    }
    .warning {
      color: #f44336;
      font-weight: 500;
    }
  `]
})
export class DeleteRideDialogComponent {
  constructor(
    public dialogRef: MatDialogRef<DeleteRideDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: Ride
  ) {}

  onCancel(): void {
    this.dialogRef.close(false);
  }

  onConfirm(): void {
    this.dialogRef.close(true);
  }
}
