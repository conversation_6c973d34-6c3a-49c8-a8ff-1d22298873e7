.sidenav-container {
  height: 100%;
  position: relative;
}

.sidenav-container .mat-drawer-container {
  position: relative;
}

.sidenav-container .mat-drawer-content {
  margin-left: 310px !important;
  transition: margin-left 0.3s ease;
  background: #f3f3f3;
}

.sidenav-container.sidebar-collapsed .mat-drawer-content {
  margin-left: 80px !important;
}

.sidenav {
  width: 310px;
  transition: width 0.3s ease;
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  z-index: 10;
}

.sidenav.collapsed {
  width: 80px;
}

.sidenav .mat-toolbar {
  background: inherit;
  padding-left: calc(12px + 0.6rem);
}

.mat-toolbar.mat-primary {
  position: sticky;
  top: 0;
  z-index: 2;
}



mat-sidenav-content {
  overflow-x: auto;
  position: relative;
  width: 100%;
}

.sidenav-container.sidebar-collapsed mat-sidenav-content .bg-toolbar {
  width: 100%;
}

.sidenav-container.sidebar-collapsed mat-sidenav-content router-outlet {
  width: 100%;
}

.bg-toolbar {
  background-color: #f3f3f3;
  color: black;
}

.navigate-item {
  border-left: 12px solid transparent;
  padding-left: 0.6rem !important;
  height: 65px !important;
  font-size: 1.2em;
  font-weight: 500;
  color: #aeaeae !important;
  white-space: nowrap;
  overflow-x: hidden;
  text-overflow: ellipsis;
}

.navigate-item-active {
  border-left: 12px solid #66b245 !important;
  color: #66b245 !important;
}

.navigate-item:hover {
  border-left: 12px solid #66b245 !important;
  color: #66b245 !important;
}

.icon-navigate svg {
  width: 13px !important;
  height: 13px !important;
  fill: #000 !important;
  color: #000 !important;
}

.sidebar-toolbar {
  position: relative;
  transition: all 0.3s ease;
  overflow: visible;
}

.toolbar-content {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  position: relative;
}

.floating-toggle-btn {
  position: fixed;
  left: 290px;
  top: 120px;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #66b245, #5aa83a);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 8px 25px rgba(102, 178, 69, 0.3);
  z-index: 1000;
  outline: none;
  color: white;
}

.sidenav-container.sidebar-collapsed .floating-toggle-btn,
.floating-toggle-btn.collapsed {
  left: 60px;
}

.floating-toggle-btn:hover {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 12px 35px rgba(102, 178, 69, 0.4);
  background: linear-gradient(135deg, #5aa83a, #4a7c2a);
}

.floating-toggle-btn:active {
  transform: scale(0.9);
  transition: all 0.1s ease;
}

.floating-toggle-btn:focus {
  outline: none;
  box-shadow: 0 0 0 4px rgba(102, 178, 69, 0.3), 0 8px 25px rgba(102, 178, 69, 0.3);
}

.toggle-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.toggle-icon svg {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.toggle-icon.collapsed svg {
  transform: rotate(180deg);
}

.arrow-path {
  transition: all 0.3s ease;
}

.floating-toggle-btn:hover .toggle-icon svg {
  transform: scale(1.1);
}

.floating-toggle-btn:hover .toggle-icon.collapsed svg {
  transform: rotate(180deg) scale(1.1);
}

@keyframes pulse {
  0% {
    box-shadow: 0 8px 25px rgba(102, 178, 69, 0.3);
  }
  50% {
    box-shadow: 0 8px 25px rgba(102, 178, 69, 0.5), 0 0 0 8px rgba(102, 178, 69, 0.1);
  }
  100% {
    box-shadow: 0 8px 25px rgba(102, 178, 69, 0.3);
  }
}

.floating-toggle-btn:focus {
  animation: pulse 2s infinite;
}

.collapsed-logo {
  font-size: 24px;
  font-weight: bold;
  color: #66b245;
  text-align: center;
}

.mat-nav-list.collapsed {
  padding: 0;
}

.mat-nav-list.collapsed .navigate-item {
  justify-content: center;
  padding-left: 0 !important;
  border-left: none !important;
  text-align: center;
}

.mat-nav-list.collapsed .navigate-item-active {
  background-color: rgba(102, 178, 69, 0.1);
  border-left: none !important;
}

.mat-nav-list.collapsed .navigate-item:hover {
  background-color: rgba(102, 178, 69, 0.1);
  border-left: none !important;
}

.nav-text {
  transition: opacity 0.3s ease;
}

.sidenav-container.sidebar-collapsed mat-sidenav-content {
  transform: translateZ(0);
}

.sidenav-container.sidebar-collapsed .table-container {
  max-width: calc(100vw - 100px) !important;
}

.sidenav-container.sidebar-collapsed .ride-list-container {
  width: 100% !important;
  max-width: none !important;
}

@media (min-width: 1200px) {
  .sidenav-container.sidebar-collapsed .table-container table {
    min-width: calc(100vw - 120px);
  }
}

.sidenav-container.sidebar-collapsed * {
  will-change: transform;
}

.sidenav-container.sidebar-collapsed .bg-toolbar,
.sidenav-container.sidebar-collapsed .content,
.sidenav-container.sidebar-collapsed router-outlet > * {
  width: 100% !important;
  max-width: none !important;
  transition: width 0.3s ease;
}

.sidenav-container.sidebar-collapsed app-ride-list {
  width: 100% !important;
}

.sidenav-container.sidebar-collapsed app-ride-list .container-fluid {
  max-width: none !important;
  padding-left: 15px !important;
  padding-right: 15px !important;
}
