.sub-content-up {
  padding: 10px;
  background-color: #fff;
}
.mat-elevation-z2 {
  box-shadow: 0 2px 5px rgba(0, 0, 0, .1);
}

.sub-content-up {
  padding: 20px;
}

.layout-style .row {
  display: flex;
  margin-bottom: 10px;
}

.layout-style .col-2, .layout-style .col-3 {
  flex: 1;
  margin-right: 20px;
}

.mt-3 {
  margin-top: 20px;
}

.maintenance-history-section {
  background: #fff;
  padding: 15px;
  border-radius: 4px;
  margin-top: 20px;
}

.loader {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50px;
}

.btn-orange {
  background-color: orange;
}

.btn-dark-gray {
  background-color: #555;
  color: #fff;
}

.btn-green-mountain {
  background-color: green;
  color: #fff;
}

.btn-gray {
  background-color: gray;
  color: #fff;
}

.btn-sapphire {
  background-color: #0F52BA;
  color: #fff;
}

.btn-purple {
  background-color: purple;
  color: #fff;
}

.btn-red {
  background-color: red;
  color: #fff;
}

.btn-yellow {
  background-color: yellow;
  color: #000;
}

.fixed-table {
  width: 100% !important;
  table-layout: fixed;
  border-collapse: collapse;
}

.fixed-table th.mat-header-cell,
.fixed-table td.mat-cell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 8px;
  border-bottom: 1px solid #e0e0e0;
}


.fixed-table .mat-column-dueDate {
  width: 120px;
}

.fixed-table .mat-column-completedDate {
  width: 140px;
}

.fixed-table .mat-column-maintenanceType {
  width: 100px;
}

.fixed-table .mat-column-maintenanceStaff {
  width: 100px;
}

.fixed-table .mat-column-notes {
  min-width: 660px;
}

.fixed-table th.mat-header-cell {
  font-weight: 600;
  font-size: 14px;
  background-color: #f5f5f5;
}

.fixed-table td.mat-cell {
  font-size: 13px;
}

.fixed-table {
  overflow-x: auto;
  display: block;
}

.fixed-table-container {
  width: 100%;
  overflow-x: auto;
  box-sizing: border-box;
}

.fixed-table-container .fixed-table {
  width: 100%;
  box-sizing: border-box;
}

.request-maintenance-btn {
  display: inline-flex;
  align-items: center;
  background-color: #ffc107 !important;
  color: #000 !important;
  font-size: 14px;
  line-height: 16px;
  padding: 4px 10px;
}

.request-maintenance-btn mat-icon {
  margin-right: 8px;
  width: 16px;
  height: 16px;
}

.request-maintenance-btn .material-icons {
  font-size: 16px;
}

.status-container {
  display: flex;
  align-items: center;
}

.status-chip {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 16px;
  color: white;
  font-weight: 500;
  font-size: 14px;
}

.status-chip .status-text {
  margin-right: 4px;
}

.status-chip mat-icon {
  font-size: 24px;
}

.status-filled {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 120px;
}

.btn-ready {
  background-color: #4caf50;
}

.btn-grounded {
  background-color: #ff9800;
}

.btn-unavailable {
  background-color: #f44336;
}

.btn-maintenance-due {
  background-color: #9c27b0;
}

.btn-post-maintenance-checks {
  background-color: #2196f3;
}

.btn-retired {
  background-color: #607d8b;
}

.menu-item-content {
  display: flex;
  align-items: center;
}

.status-circle {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 8px;
}

.btn-ready.status-circle {
  background-color: #4caf50;
}

.btn-grounded.status-circle {
  background-color: #ff9800;
}

.btn-unavailable.status-circle {
  background-color: #f44336;
}

.btn-maintenance-due.status-circle {
  background-color: #9c27b0;
}

.btn-post-maintenance-checks.status-circle {
  background-color: #2196f3;
}

.btn-retired.status-circle {
  background-color: #607d8b;
}

mat-menu .mat-menu-item {
  padding: 8px 16px;
}

mat-menu .mat-menu-item:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.status-chip {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 8px;
}

.status-text {
  font-weight: 500;
  font-size: 14px;
}

.toggle-ready .mat-slide-toggle-bar {
  background-color: #4caf50 !important;
}

.toggle-ready .mat-slide-toggle-thumb {
  background-color: #ffffff !important;
}

.toggle-unavailable .mat-slide-toggle-bar {
  background-color: #f44336 !important;
}

.toggle-unavailable .mat-slide-toggle-thumb {
  background-color: #ffffff !important;
}

.mat-slide-toggle.mat-primary.mat-checked .mat-slide-toggle-bar {
  background-color: #4caf50 !important;
}

.mat-slide-toggle.mat-warn.mat-checked .mat-slide-toggle-bar {
  background-color: #f44336 !important;
}

.sw-dropdown-container {
  display: flex;
  align-items: center;
  gap: 4px;
}

.sw-dropdown-btn {
  margin-left: 2px;

  .mat-icon {
    font-size: 20px;
    height: 20px;
    width: 20px;
    line-height: 20px;
  }
}

.menu-with-search {
  .search-container {
    padding: 8px;

    .search-field {
      width: 100%;

      .mat-form-field-wrapper {
        padding-bottom: 0;
      }

      input {
        height: 40px;
      }
    }
  }
  .mat-form-field-underline {
    position: relative;
  }

  .mat-menu-item {
    height: 36px;
    line-height: 36px;
    font-size: 14px;

    .mat-icon {
      margin-right: 8px;
      vertical-align: middle;
    }
  }
}

.mat-divider {
  margin: 4px 0;
}

.mat-menu-panel {
  min-height: auto !important;
}

.mat-menu-content:not(:empty) {
  padding: 0;
}

::ng-deep .mat-menu-panel {
  min-width: 200px !important;


  .mat-form-field-appearance-fill {
    .mat-form-field-flex {
      border-radius: 4px;
      background-color: #fff;
    }

    .mat-form-field-underline {
      display: none;
    }
  }
}

.mat-form-field-appearance-fill .mat-form-field-flex {
  background-color: #fff;
}

.version-value {
  color: #333;
  font-family: monospace;
  padding: 2px 6px;
  background-color: #e9ecef;
  border-radius: 3px;
}

.copy-button .mat-icon {
  width: 12px;
  height: 12px;
  line-height: 12px;
}

.data-label {
  font-weight: 600;
  color: #444;
  margin-right: 4px;
}

.data-value {
  color: #333;
}

.missing-data {
  color: #999;
  font-style: italic;
}

.data-label {
  font-weight: 600;
  color: #444;
  margin-right: 4px;
}

.data-value {
  color: #333;
}

.sw-row {
  padding: 4px 8px;
  border-radius: 4px;
  margin-bottom: 8px;
  display: inline-block;
}

.version-value {
  color: green;
  margin-left: 4px;
}

.toggle-ready {
  color: green;
}

.toggle-unavailable {
  color: red;
}

.status-chip {
  padding: 4px 8px;
  border-radius: 8px;
  cursor: pointer;
}

.status-text {
  margin-right: 4px;
}

.menu-item-content .status-circle {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
}

.data-label {
  font-weight: 600;
  color: #444;
  margin-right: 4px;
}

.data-value {
  color: #333;
}

.software-section-title {
  margin-top: 1rem;
  margin-bottom: 1rem;
  font-size: 1.2rem;
  font-weight: 600;
  color: #444;
  border-bottom: 1px solid #ddd;
  padding-bottom: 0.4rem;
}

.software-group-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 0.8rem;
  color: #555;
}


.sw-row {
  padding: 6px 8px;
  border-radius: 4px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: fit-content;
}

.version-value {
  color: green;
  margin-left: 4px;
  margin-right: 8px;
}

.toggle-ready {
  color: green;
}

.toggle-unavailable {
  color: red;
}

.menu-item-content .status-circle {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
}

.software-card {
  padding: 16px;
  margin-top: 16px;
  background: #fcfcfc;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
}

.software-group-title {
  margin-top: 10px;
  margin-bottom: 10px;
  font-weight: 600;
}
.software-item {
  background: #f9f9f9;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.label-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  margin-bottom: 4px;
}

.desired-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 6px;
}

.desired-label {
  min-width: 140px;
  font-weight: 500;
}

.software-version-badge {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  background-color: #eef;
  font-family: 'Roboto Mono', monospace;
  font-size: 0.9rem;
  color: #333;
  margin-left: 6px;
}

.warning-border {
  box-shadow: 0 0 0 2px #ffeb3b inset;
  border-radius: 4px;
}

.icon-yellow {
  color: #ffc107;
}

.search-container {
  padding: 8px 16px 0;
}
.search-field {
  width: 220px;
}
.small-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
  line-height: 18px;
}

.copy-button {
  width: 24px;
  height: 24px;
  line-height: 24px;
  padding: 0;
}

.current-status-row {
  background-color: rgba(0, 0, 0, 0.04);
}

.current-status-row:hover {
  background-color: rgba(0, 0, 0, 0.08);
}

.status-history-link {
  cursor: pointer;
  color: #3f51b5;
}

.status-chip {
  cursor: pointer;
}

@keyframes highlight-section {
  0% {
    background-color: rgba(63, 81, 181, 0.1);
  }
  100% {
    background-color: transparent;
  }
}

#statusHistorySection.highlight {
  animation: highlight-section 2s ease-out;
}

::ng-deep .status-tooltip {
  font-size: 14px !important;
  line-height: 1.5 !important;
  max-width: 350px !important;
  white-space: pre-line !important;
  padding: 12px !important;
}

