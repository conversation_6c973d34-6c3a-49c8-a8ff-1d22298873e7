import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Battery } from '@app/core/interfaces/battery.interface';
import { Response } from '@app/core/interfaces/responses/response.interface';
import { typeAction } from '@app/shared/utils/enum';
import { environment } from '@src/environments/environment';
import { Observable, Subject } from 'rxjs';

@Injectable()
export class BatteryService {
  private apiUrl = environment.urlMsBattery;
  private subjectBatteries = new Subject<Battery[]>();
  public action: typeAction | undefined;

  constructor(private http: HttpClient) {}

  getBatteryList(params?: HttpParams): Observable<Response<Battery[]>> {
    return this.http.get<Response<Battery[]>>(`${this.apiUrl}.json`, {
      params: params,
    });
  }

  createBattery(battery: any): Observable<Response<Battery>> {
    return this.http.post<Response<Battery>>(`${this.apiUrl}.json`, battery);
  }

  updateBattery(
    batteryId: number,
    battery: any
  ): Observable<Response<Battery>> {
    return this.http.patch<Response<Battery>>(
      `${this.apiUrl}/${batteryId}.json`,
      battery
    );
  }

  notifyDataSubject(status: Battery[], action?: typeAction): void {
    this.action = action;
    this.subjectBatteries.next(status);
  }

  listenDataSubjectBattery(): Observable<Battery[]> {
    return this.subjectBatteries.asObservable();
  }
}
