import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Customer } from '@app/core/interfaces/customer.interface';
import { environment } from '@src/environments/environment';
import { KeycloakService } from 'keycloak-angular';
import { Observable, from } from 'rxjs';
import { switchMap } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class CustomerService {
  private apiUrl = environment.urlMsRides;

  constructor(private http: HttpClient, private keycloakService: KeycloakService) { }

  getCustomersList(skip: number = 0, limit: number = 1000): Observable<Customer[]> {
    return from(this.keycloakService.getToken()).pipe(
      switchMap((token) => {
        const headers = new HttpHeaders({
          Authorization: `Bearer ${token}`,
        });

        let params = new HttpParams()
          .set('skip', skip.toString())
          .set('limit', limit.toString());

        return this.http.get<Customer[]>(`${this.apiUrl}/customers`, { headers, params });
      })
    );
  }
}
