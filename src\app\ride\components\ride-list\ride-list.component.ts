import {
  Component,
  OnInit,
  AfterViewInit,
  ViewChild,
  OnDestroy,
  Output,
  EventEmitter,
  ViewEncapsulation,
} from '@angular/core';
import {DatePipe} from '@angular/common';
import {fork<PERSON>oin, of, PartialObserver, Subject, Observable} from 'rxjs';
import {ActivatedRoute, Router} from '@angular/router';

import {MatPaginator, PageEvent} from '@angular/material/paginator';
import {MatTableDataSource} from '@angular/material/table';
import {MatDialog} from '@angular/material/dialog';

import {NotificationsService} from '@shared/services/notifications.service';

import {Ride, RideStatus, Route} from '@app/core/interfaces/ride.interface';
import {RideTiming} from '@app/core/interfaces/ride-timing.interface';
import {RideTimingService} from '@app/services/ride-timing/ride-timing.service';
import {CancellationReason} from '@app/core/interfaces/cancellation-reason.interface';
import {CancellationReasonService} from '@app/services/cancellation-reason/cancellation-reason.service';
import {RideService} from '@app/services/ride/ride.service';
import {TimeTrackingService} from '@app/services/time-tracking/time-tracking.service';
import {TimeTrackingDescriptionDialogComponent} from '../time-tracking-description-dialog/time-tracking-description-dialog.component';
import {DialogRideOperatorComponent} from '../dialog-ride-operator/dialog-ride-operator.component';
import {DialogRideGliderComponent} from '../dialog-ride-glider/dialog-ride-glider.component';

import {catchError, debounceTime, map, switchMap, takeUntil, startWith} from 'rxjs/operators';
import {SharedVarService} from '@app/services/SharedVarService/shared-var-service.service';
import {Location} from '@app/core/interfaces/location.interface';
import {FormControl} from '@angular/forms';
import {RouteService} from '@app/services/route/route.service';
import {DialogLocationInfoComponent} from '@app/shared/components/dialog-location-information/dialog-location-information.component';
import {
  ReplaceElementArray,
  ReplaceElementObjectArray,
} from '@app/shared/utils/utils';
import {typeAction} from '@app/shared/utils/enum';
import {rideStatusKey} from '@app/ride/enum/enum';
import {LayoutRideInfoComponent} from '../layout-ride-info/layout-ride-info.component';
import {UserService} from '@app/services/user/user.service';
import {LocationService} from '@app/services/location/location.service';
import {FleetService} from '@app/services/fleet/fleet.service';
import {Glider} from '@app/core/interfaces/glider.interace';
import {CustomerService} from '@app/services/customer/customer.service';
import {Customer} from '@app/core/interfaces/customer.interface';
import {UserData} from '@app/core/interfaces/userdata.interface';
import {DialogRideInfoComponent} from '@shared/components/dialog-ride-information/dialog-ride-information.component';
import {CancelReasonDialogComponent} from '@app/ride/components/dialog-cancel-reason/cancel-reason-dialog.component';
import {DeleteRideDialogComponent} from '../dialog-delete-ride/delete-ride-dialog.component';
import {AuthService} from '@app/services/auth.service';

export interface ApiResponse<T> {
  data: T;
}

@Component({
  selector: 'app-ride-list',
  templateUrl: './ride-list.component.html',
  styleUrls: ['./ride-list.component.css'],
  encapsulation: ViewEncapsulation.None,
})
export class RideListComponent implements OnInit, OnDestroy, AfterViewInit {
  displayedColumns: string[] = [
    'time_departure',
    'date_departure',
    'glider',
    'operator',
    'location_departure',
    'location_arrival',
    'time_arrival',
    'status',
    'reason',
    'timetracking',
    'actions',
  ];
  dataSource = new MatTableDataSource<Ride>();
  subject = new Subject<any>();
  data: Ride[] = [];
  locations: Location[] = [];
  filterStatus: RideStatus[] = [];
  status = rideStatusKey;

  gliders: Glider[] = [];
  users: UserData[] = [];
  routes: Route[] = [];
  customers: Customer[] = [];
  isLoading = false;

  statusFilter = new FormControl('');
  operatorFilter = new FormControl('');
  operatorSearchControl = new FormControl('');
  filteredUsers: UserData[] = [];

  routeFilter = new FormControl('');
  customerFilter = new FormControl('');
  departureTimeFromFilter = new FormControl(null);
  departureTimeToFilter = new FormControl(null);

  totalRides = 0;
  pageSize = 20;
  pageIndex = 0;

  rideId: number;

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @Output() eventData = new EventEmitter<boolean>();



  selectedRideForHistory: number | null = null;
  cancellationReasons: CancellationReason[] = [];

  constructor(
    private sharedVarService: SharedVarService,
    private rideService: RideService,
    private userService: UserService,
    public dialog: MatDialog,
    private notificationsAlerts: NotificationsService,
    private locationService: LocationService,
    private fleetService: FleetService,
    private routeService: RouteService,
    private customerService: CustomerService,
    private datePipe: DatePipe,
    public rideTimingService: RideTimingService,
    private authService: AuthService,
    private timeTrackingService: TimeTrackingService,
    private cancellationReasonService: CancellationReasonService,
    private router: Router,
    private route: ActivatedRoute
  ) {
  }

  ngOnInit(): void {
    this.sharedVarService.setValue('Rides');
    this.loadFiltersFromUrl();
    this.loadData();
    this.listenDataSubject();

    this.rideService
      .getRidesStatusList()
      .pipe(
        takeUntil(this.subject),
        catchError((err) => {
          this.notificationsAlerts.openSnackBar('Error', 'Ok');
          return of(err);
        })
      )
      .subscribe((res) => {
        this.filterStatus = res;
      });

    this.cancellationReasonService
      .getCancellationReasons()
      .pipe(
        takeUntil(this.subject),
        catchError((err) => {
          return of([]);
        })
      )
      .subscribe((reasons) => {
        this.cancellationReasons = reasons;
      });

    this.fieldListener();
  }

  private filterOperators(searchText: string): void {
    if (!searchText) {
      this.filteredUsers = this.users;
      return;
    }

    const filterValue = searchText.toLowerCase();
    this.filteredUsers = this.users.filter(user =>
      (user.email && user.email.toLowerCase().includes(filterValue)) ||
      (user.first_name && user.first_name.toLowerCase().includes(filterValue)) ||
      (user.last_name && user.last_name.toLowerCase().includes(filterValue))
    );
  }

  private listenDataSubject(): void {

    this.rideService
      .listenDataSubjectRide()
      .pipe(
        takeUntil(this.subject),
        catchError((err) => {
          this.notificationsAlerts.openSnackBar('Error', 'Ok');
          return of(err);
        })
      )
      .subscribe((res) => {
        if (this.rideService.action === typeAction.updated) {

          const index = this.data.findIndex(
            (element, index) => element.id === res.id
          );

          const user = (this.users as any[]).find(user => String(user.id) === res.operator_id);
          this.data[index].operator_email = user?.email || 'Unknown';
          this.data[index].glider_name = res.glider_name;

          this.dataSource.data = this.data;

        }
      });

    this.userService
      .listenDataSubjectUser()
      .pipe(
        takeUntil(this.subject),
        catchError((err) => {
          this.notificationsAlerts.openSnackBar('Error', 'Ok');
          return of(err);
        })
      )
      .subscribe((res) => {
        if (this.userService.action === typeAction.updated) {
          this.data = this.dataSource.data = ReplaceElementObjectArray(
            this.data,
            this.rideId,
            res,
            'owner'
          );
        }
      });
  }

  ngAfterViewInit(): void {
    this.paginator.page.pipe(
      takeUntil(this.subject)
    ).subscribe((pageEvent: PageEvent) => {
      this.pageSize = pageEvent.pageSize;
      this.pageIndex = pageEvent.pageIndex;
      this.applyFilters();
    });
  }

  loadData(): void {
    this.isLoading = true;
    this.loadFilteredData();
  }

  private fieldListener() {
    this.statusFilter.valueChanges.pipe(
      takeUntil(this.subject),
      debounceTime(300)
    ).subscribe(() => {
      this.pageIndex = 0;
      if (this.paginator) {
        this.paginator.pageIndex = 0;
      }
      this.applyFilters();
    });

    this.operatorFilter.valueChanges.pipe(
      takeUntil(this.subject),
      debounceTime(300)
    ).subscribe(() => {
      this.pageIndex = 0;
      if (this.paginator) {
        this.paginator.pageIndex = 0;
      }
      this.applyFilters();
    });

    this.routeFilter.valueChanges.pipe(
      takeUntil(this.subject),
      debounceTime(300)
    ).subscribe(() => {
      this.pageIndex = 0;
      if (this.paginator) {
        this.paginator.pageIndex = 0;
      }
      this.applyFilters();
    });

    this.departureTimeFromFilter.valueChanges.pipe(
      takeUntil(this.subject),
      debounceTime(300)
    ).subscribe(() => {
      this.pageIndex = 0;
      if (this.paginator) {
        this.paginator.pageIndex = 0;
      }
      this.applyFilters();
    });

    this.departureTimeToFilter.valueChanges.pipe(
      takeUntil(this.subject),
      debounceTime(300)
    ).subscribe(() => {
      this.pageIndex = 0;
      if (this.paginator) {
        this.paginator.pageIndex = 0;
      }
      this.applyFilters();
    });

    this.customerFilter.valueChanges.pipe(
      takeUntil(this.subject),
      debounceTime(300)
    ).subscribe(() => {
      this.pageIndex = 0;
      if (this.paginator) {
        this.paginator.pageIndex = 0;
      }
      this.applyFilters();
    });

    this.operatorSearchControl.valueChanges.pipe(
      takeUntil(this.subject),
      debounceTime(300)
    ).subscribe((searchText) => {
      this.filterOperators(searchText);
    });
  }

  private applyFilters(): void {
    this.updateUrlParams();
    this.loadFilteredData(
      this.operatorFilter.value || undefined,
      this.getStatusIdFromName(this.statusFilter.value),
      undefined,
      this.routeFilter.value || undefined,
      this.departureTimeFromFilter.value ? this.formatDatetime(this.departureTimeFromFilter.value) : undefined,
      this.departureTimeToFilter.value ? this.formatDatetime(this.departureTimeToFilter.value, true) : undefined,
      this.customerFilter.value || undefined
    );
  }

  private getStatusIdFromName(statusName: string): number | undefined {
    if (!statusName) {
      return undefined;
    }
    const status = this.filterStatus.find(s => s.name === statusName);
    return status ? status.id : undefined;
  }

  loadFilteredData(operatorId?: string, statusId?: number, gliderName?: string, routeId?: number, start_time?: string, end_time?: string, customerId?: number): void {
    this.isLoading = true;
    const skip = this.pageIndex * this.pageSize;
    const limit = this.pageSize;

    forkJoin({
      rides: this.rideService.getRideList(
        skip,
        limit,
        operatorId,
        statusId,
        gliderName,
        routeId,
        start_time,
        end_time,
        customerId
      ),
      users: this.userService.getUserList(),
      routes: this.routeService.getRoutesList(),
      gliders: this.fleetService.getFleetList(),
      locations: this.locationService.getLocationsList(),
      customers: this.customerService.getCustomersList()
    }).pipe(
      takeUntil(this.subject),
      catchError((err) => {
        this.notificationsAlerts.openSnackBar('Error', 'Ok');
        this.isLoading = false;
        return of({
          rides: [],
          users: {data: []},
          routes: {data: []},
          gliders: {data: []},
          locations: {data: []},
          customers: []
        });
      })
    ).subscribe((res) => {
      this.users = Array.isArray(res.users) ? res.users : res.users.data;
      this.routes = Array.isArray(res.routes) ? res.routes : res.routes.data;
      this.gliders = Array.isArray(res.gliders) ? res.gliders : res.gliders.data;
      this.locations = Array.isArray(res.locations) ? res.locations : res.locations.data;
      this.customers = Array.isArray(res.customers) ? res.customers : (res.customers as any)?.data || [];
      this.filteredUsers = this.users;

      let rides: Ride[] = [];
      if (Array.isArray(res.rides)) {
        rides = res.rides;
        this.totalRides = rides.length === this.pageSize ? (this.pageIndex + 2) * this.pageSize : (this.pageIndex * this.pageSize) + rides.length;
      } else {
        rides = res.rides.data || [];
        this.totalRides = res.rides.total || (res.rides as any).count || rides.length;
      }

      this.data = rides.map(ride => {
        let operatorEmail = 'Unknown';
        if (ride.operator_id && this.users && this.users.length > 0) {
          const user = this.users.find(user => String(user.id) === String(ride.operator_id));
          operatorEmail = user?.email || (user?.first_name && user?.last_name ? `${user.first_name} ${user.last_name}` : 'Unknown');
        }
        let fromLocationName = 'Unknown Location';
        let toLocationName = 'Unknown Location';

        if (ride.route) {
          fromLocationName = ride.route.start_location_name || 'Unknown Location';
          toLocationName = ride.route.end_location_name || 'Unknown Location';
        } else if (this.locations && this.locations.length > 0) {
          const fromLocation = this.locations.find(loc => String(loc.id) === String(ride.from_location));
          const toLocation = this.locations.find(loc => String(loc.id) === String(ride.to_location));
          fromLocationName = fromLocation?.name || 'Unknown Location';
          toLocationName = toLocation?.name || 'Unknown Location';
        }
        let gliderName = ride.glider_name || 'Unknown';
        let gliderStatus = ride.glider_status;

        if (ride.glider_id && this.gliders && this.gliders.length > 0) {
          const glider = this.gliders.find(g => String(g.id) === String(ride.glider_id));
          if (glider) {
            gliderName = glider.name || gliderName;
            gliderStatus = glider.status || glider.gliderStatus || ride.glider_status;
          }
        }

        const enrichedRide = {
          ...ride,
          operator_email: ride.operator_email || operatorEmail,
          from_location_name: ride.from_location_name || fromLocationName,
          to_location_name: ride.to_location_name || toLocationName,
          glider_name: gliderName,
          glider_status: gliderStatus
        };

        return enrichedRide;
      });

      this.dataSource.data = this.data;
      this.isLoading = false;
    });
  }

  onPageChange(event: PageEvent): void {
    this.pageSize = event.pageSize;
    this.pageIndex = event.pageIndex;
    this.updateUrlParams();
    this.loadFilteredData(
      this.operatorFilter.value || undefined,
      this.getStatusIdFromName(this.statusFilter.value),
      undefined,
      this.routeFilter.value || undefined,
      this.departureTimeFromFilter.value ? this.formatDatetime(this.departureTimeFromFilter.value) : undefined,
      this.departureTimeToFilter.value ? this.formatDatetime(this.departureTimeToFilter.value, true) : undefined,
      this.customerFilter.value || undefined
    );
  }

  clearfilters(): void {
    this.statusFilter.setValue('');
    this.operatorFilter.setValue('');
    this.routeFilter.setValue('');
    this.customerFilter.setValue('');
    this.departureTimeFromFilter.setValue(null);
    this.departureTimeToFilter.setValue(null);
    this.pageIndex = 0;
    if (this.paginator) {
      this.paginator.pageIndex = 0;
    }
    this.router.navigate([], {
      relativeTo: this.route,
      queryParams: {}
    });
    this.loadFilteredData();
  }

  formatDate(date: Date | string | null): string {
    if (!date) {
      return '';
    }
    return this.datePipe.transform(date, 'yy/MM/dd') || '';
  }

  formatDatetime(date: Date | string | null, endOfDay: boolean = false): string {
    if (!date) {
      return '';
    }
    if (typeof date === 'string') {
      const dateObj = new Date(date);
      if (endOfDay && !date.includes('T')) {
        dateObj.setHours(23, 59, 59, 999);
      }
      return dateObj.toISOString();
    }
    const dateObj = new Date(date);
    if (endOfDay) {
      dateObj.setHours(23, 59, 59, 999);
    }
    return dateObj.toISOString();
  }



  private loadFiltersFromUrl(): void {
    const params = this.route.snapshot.queryParams;

    if (params['status']) {
      this.statusFilter.setValue(params['status']);
    }

    if (params['operator']) {
      this.operatorFilter.setValue(parseInt(params['operator'], 10));
    }

    if (params['route']) {
      this.routeFilter.setValue(parseInt(params['route'], 10));
    }

    if (params['customer']) {
      this.customerFilter.setValue(parseInt(params['customer'], 10));
    }

    if (params['dateFrom']) {
      const date = new Date(params['dateFrom']);
      const localDatetime = date.getFullYear() + '-' +
        String(date.getMonth() + 1).padStart(2, '0') + '-' +
        String(date.getDate()).padStart(2, '0') + 'T' +
        String(date.getHours()).padStart(2, '0') + ':' +
        String(date.getMinutes()).padStart(2, '0');
      this.departureTimeFromFilter.setValue(localDatetime);
    }

    if (params['dateTo']) {
      const date = new Date(params['dateTo']);
      const localDatetime = date.getFullYear() + '-' +
        String(date.getMonth() + 1).padStart(2, '0') + '-' +
        String(date.getDate()).padStart(2, '0') + 'T' +
        String(date.getHours()).padStart(2, '0') + ':' +
        String(date.getMinutes()).padStart(2, '0');
      this.departureTimeToFilter.setValue(localDatetime);
    }

    if (params['page']) {
      this.pageIndex = parseInt(params['page'], 10);
    }

    if (params['pageSize']) {
      this.pageSize = parseInt(params['pageSize'], 10);
    }

    setTimeout(() => {
      if (this.paginator) {
        this.paginator.pageIndex = this.pageIndex;
        this.paginator.pageSize = this.pageSize;
      }
    });
  }

  private updateUrlParams(): void {
    const queryParams: any = {};

    if (this.statusFilter.value) {
      queryParams.status = this.statusFilter.value;
    }

    if (this.operatorFilter.value) {
      queryParams.operator = this.operatorFilter.value;
    }

    if (this.routeFilter.value) {
      queryParams.route = this.routeFilter.value;
    }

    if (this.customerFilter.value) {
      queryParams.customer = this.customerFilter.value;
    }

    if (this.departureTimeFromFilter.value) {
      queryParams.dateFrom = this.formatDatetime(this.departureTimeFromFilter.value);
    }

    if (this.departureTimeToFilter.value) {
      queryParams.dateTo = this.formatDatetime(this.departureTimeToFilter.value, true);
    }

    if (this.pageIndex > 0) {
      queryParams.page = this.pageIndex;
    }

    if (this.pageSize !== 20) {
      queryParams.pageSize = this.pageSize;
    }

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams
    });
  }



  ngOnDestroy(): void {
    this.subject.complete();
    this.subject.unsubscribe();
  }

  startTimer(rideId: number): void {
    const ride = this.data.find(r => r.id === rideId);

    let routeId = ride?.route_id;
    if (!routeId && ride?.route?.id) {
      routeId = ride.route.id;
    }
    if (!routeId) {
      routeId = 1;
    }

    if (ride) {
      const dialogRef = this.dialog.open(TimeTrackingDescriptionDialogComponent, {
        width: '450px',
        data: {rideId, routeId}
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          this.rideTimingService.startTimer(rideId, routeId, result.description);
        }
      });
    } else {
      this.notificationsAlerts.openSnackBar('Cannot start timer: ride not found', 'Ok');
    }
  }

  stopTimer(rideId: number): void {
    const ride = this.data.find(r => r.id === rideId);

    let routeId = ride?.route_id;
    if (!routeId && ride?.route?.id) {
      routeId = ride.route.id;
    }

    if (!routeId) {
      routeId = 1;
    }

    if (ride) {
      this.rideTimingService.stopTimer(rideId, undefined, routeId);
    } else {
      this.rideTimingService.stopTimer(rideId);
    }
  }

  resetTimer(rideId: number): void {
    this.rideTimingService.resetTimer(rideId);
  }

  isTimerRunning(rideId: number): boolean {
    return this.rideTimingService.isTimerRunning(rideId);
  }

  getFormattedTime(rideId: number): string {
    return this.rideTimingService.getFormattedTime(rideId);
  }

  showTimingHistory(rideId: number): void {
    this.rideTimingService.loadServerTimings(rideId);
    this.selectedRideForHistory = rideId;
  }

  hideTimingHistory(): void {
    this.selectedRideForHistory = null;
  }

  isAdmin(): boolean {
    const roles = this.authService.getUserRoles();
    return roles.some(role =>
      role === 'view-users'
    );
  }

  deleteRide(ride: Ride): void {
    const dialogRef = this.dialog.open(DeleteRideDialogComponent, {
      width: '500px',
      data: ride
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.rideService.deleteRide(ride.id).subscribe(
          () => {
            this.data = this.data.filter(item => item.id !== ride.id);
            this.dataSource.data = this.data;
            this.notificationsAlerts.openSnackBar('Ride deleted successfully', 'Ok');
          },
          error => {
            this.notificationsAlerts.openSnackBar('Error deleting ride', 'Ok');
          }
        );
      }
    });
  }

  getTimingsForRide(rideId: number): RideTiming[] {
    return this.rideTimingService.getTimingsForRide(rideId);
  }

  getCancellationReasonDetails(reason: any): CancellationReason | null {
    if (!reason) {
      return null;
    }

    if (typeof reason === 'object' && reason !== null) {
      if (reason.id && reason.name) {
        let cancellationReason = this.cancellationReasons.find(r => String(r.id) === String(reason.id));
        if (cancellationReason) {
          return cancellationReason;
        }

        const reasonName = String(reason.name).toLowerCase();
        cancellationReason = this.cancellationReasons.find(r => r.name.toLowerCase() === reasonName);
        if (cancellationReason) {
          return cancellationReason;
        }

        return this.determineCategoryFromName(reasonName);
      }
      return null;
    }

    if (typeof reason === 'string') {
      const reasonStr = reason.toLowerCase();

      let cancellationReason = this.cancellationReasons.find(r =>
        r.name.toLowerCase() === reasonStr ||
        String(r.id) === reasonStr
      );

      if (!cancellationReason) {
        return this.determineCategoryFromName(reasonStr);
      }

      return cancellationReason;
    }

    return null;
  }

  private determineCategoryFromName(reasonStr: string): CancellationReason | null {
    if (reasonStr.includes('environmental')) {
      return this.cancellationReasons.find(r => r.category === 'Environmental') || null;
    } else if (reasonStr.includes('customer') || reasonStr.includes('client')) {
      return this.cancellationReasons.find(r => r.category === 'Customer') || null;
    } else if (reasonStr.includes('technical') || reasonStr.includes('issue')) {
      return this.cancellationReasons.find(r => r.category === 'Technical') || null;
    } else if (reasonStr.includes('operations') || reasonStr.includes('team')) {
      return this.cancellationReasons.find(r => r.category === 'Operations') || null;
    }
    return null;
  }


  getStatusBadgeColor(status: any, reason?: any): string {
    const statusName = typeof status === 'object' && status !== null && status.name
      ? status.name.toLowerCase()
      : typeof status === 'string' ? status.toLowerCase() : '';

    if (statusName === 'cancelled' && reason) {
      const reasonDetails = this.getCancellationReasonDetails(reason);
      return reasonDetails ? reasonDetails.color : '#f44336';
    }

    switch (statusName) {
      case 'pending':
        return '#607d8b';
      case 'in flight':
      case 'in-flight':
        return '#2196f3';
      case 'completed':
        return '#4caf50';
      case 'cancelled':
        return '#f44336';
      default:
        return '#9e9e9e';
    }
  }

  getFormattedStatusText(status: any, reason?: any): string {
    const statusName = typeof status === 'object' && status !== null && status.name
      ? status.name
      : typeof status === 'string' ? status : '';

    const statusLower = statusName.toLowerCase();

    if (statusLower === 'cancelled' && reason) {
      const reasonDetails = this.getCancellationReasonDetails(reason);
      if (reasonDetails) {
        return `Cancelled (${reasonDetails.category})`;
      }
    }

    return statusName;
  }

  getStatusDescription(statusId: number, reason?: any): string {
    const status = this.filterStatus.find(s => s.id === statusId);
    if (!status) {
      return '';
    }

    if (status.name.toLowerCase() === 'cancelled' && reason) {
      const reasonDetails = this.getCancellationReasonDetails(reason);
      const reasonText = typeof reason === 'object' && reason !== null && reason.name
        ? reason.name
        : typeof reason === 'string' ? reason : '';

      if (reasonDetails) {
        return `Status: ${status.name}\nReason: ${reasonText}\nCategory: ${reasonDetails.category}`;
      }
      return `Status: ${status.name}\nReason: ${reasonText}`;
    }

    if (status.name.toLowerCase() === 'cancelled') {
      return `Status: ${status.name}\n\nPossible reasons:\n${this.cancellationReasons.map(r => `• ${r.name}`).join('\n')}`;
    }

    return `Status: ${status.name}${status.description ? '\nDescription: ' + status.description : ''}`;
  }

  getDetailedStatusTooltip(status: any, reason?: any): string {
    const statusName = typeof status === 'object' && status !== null && status.name
      ? status.name
      : typeof status === 'string' ? status : '';

    const statusLower = statusName.toLowerCase();

    if (statusLower === 'cancelled' && reason) {
      const reasonDetails = this.getCancellationReasonDetails(reason);
      const reasonText = typeof reason === 'object' && reason !== null && reason.name
        ? reason.name
        : typeof reason === 'string' ? reason : '';

      if (reasonDetails) {
        return `Status: ${statusName}\nReason: ${reasonText}\nCategory: ${reasonDetails.category}`;
      }
      return `Status: ${statusName}\nReason: ${reasonText}`;
    }

    const statusObj = this.filterStatus.find(s => s.name === statusName);
    return `Status: ${statusName}${statusObj?.description ? '\nDescription: ' + statusObj.description : ''}`;
  }

  isGliderWarningStatus(gliderStatus: any): boolean {
    if (!gliderStatus) {
      return false;
    }
    const statusName = gliderStatus.name?.toLowerCase() || '';
    const shouldShow = statusName === 'grounded' || statusName === 'unavailable';
    return shouldShow;
  }

  getGliderStatusTooltip(gliderStatus: any): string {
    if (!gliderStatus) {
      return 'No status information available';
    }
    return `Drone Status: ${gliderStatus.name || 'Unknown'}`;
  }

  getGliderWarningIconClass(gliderStatus: any): string {
    if (!gliderStatus) {
      return 'warning-icon-red';
    }
    const statusName = gliderStatus.name?.toLowerCase() || '';
    return statusName === 'grounded' ? 'warning-icon-yellow' : 'warning-icon-red';
  }

  getReasonText(reason: any): string {
    if (!reason) {
      return '';
    }

    if (typeof reason === 'object' && reason !== null) {
      return reason.name || reason.description || '';
    }

    return String(reason);
  }

  openDialogEdit(row: Ride): void {
    this.dialog.open(LayoutRideInfoComponent, {
      height: 'auto',
      width: '600px',
      data: row,
    });
  }

  openDialogLocation(row: Location): void {
    this.dialog.open(DialogLocationInfoComponent, {
      height: 'auto',
      width: '600px',
      data: row,
    });
  }

  openDialogOperator(row: Ride): void {
    this.dialog.open(DialogRideOperatorComponent, {
      height: 'auto',
      width: '600px',
      data: row,
    });
  }

  openDialogGlider(row: Ride): void {
    this.dialog.open(DialogRideGliderComponent, {
      height: 'auto',
      width: '600px',
      data: row,
    });
  }

  getNextStatus(element: Ride): RideStatus | undefined {
    if (!this.filterStatus || this.filterStatus.length === 0) {
      return undefined;
    }
    const currentIndex = this.filterStatus.findIndex(status => status.id === element.ride_status_id);
    return currentIndex === this.filterStatus.length - 1
      ? this.filterStatus[0]
      : this.filterStatus[currentIndex + 1];
  }

  onStatusUpdate(element: Ride): void {
    element.updating = true;

    const currentIndex = this.filterStatus.findIndex(status => status.id === element.ride_status_id);
    const nextStatus = currentIndex === this.filterStatus.length - 1
      ? this.filterStatus[0]
      : this.filterStatus[currentIndex + 1];

    if (nextStatus.name.toLowerCase() === 'cancelled') {
      const dialogRef = this.dialog.open(CancelReasonDialogComponent, {
        width: '400px'
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          const updatedRide = {
            ride_status_id: nextStatus.id,
            cancel_reason: result.reason,
            delivered_by_car: result.delivered_by_car,
            cancel_reason_id: result.cancel_reason_id
          };

          this.rideService.updateRide(element.id, updatedRide).subscribe(
            (response) => {
              element.updating = false;
              element.ride_status_id = nextStatus.id;
              element.ride_status = nextStatus;
              element.cancel_reason = updatedRide.cancel_reason;
              if (updatedRide.delivered_by_car !== undefined) {
                element.delivered_by_car = updatedRide.delivered_by_car;
              }
              this.dataSource.data = [...this.data];
              this.notificationsAlerts.openSnackBar('Status updated successfully', 'Ok');
            },
            () => {
              element.updating = false;
              this.notificationsAlerts.openSnackBar('Error updating status', 'Ok');
            }
          );
        } else {
          element.updating = false;
        }
      });
    } else {
      const updatedRide = {
        ride_status_id: nextStatus.id
      };

      this.rideService.updateRide(element.id, updatedRide).subscribe(
        (response) => {
          element.updating = false;
          element.ride_status_id = nextStatus.id;
          element.ride_status = nextStatus;
          this.dataSource.data = [...this.data];
          this.notificationsAlerts.openSnackBar('Status updated successfully', 'Ok');
        },
        () => {
          element.updating = false;
          this.notificationsAlerts.openSnackBar('Error updating status', 'Ok');
        }
      );
    }
  }

  openDialogEditRides(row: Ride): void {
    const dialogRef = this.dialog.open(DialogRideInfoComponent, {
      height: 'auto',
      width: '700px',
      data: row,
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.applyFilters();
      }
    });
  }

  exportRidesToCSV(): void {
    const operatorId = this.operatorFilter.value || undefined;
    const statusId = this.getStatusIdFromName(this.statusFilter.value);
    const routeId = this.routeFilter.value || undefined;
    const customerId = this.customerFilter.value || undefined;
    const startTime = this.departureTimeFromFilter.value ? this.formatDatetime(this.departureTimeFromFilter.value) : undefined;
    const endTime = this.departureTimeToFilter.value ? this.formatDatetime(this.departureTimeToFilter.value, true) : undefined;

    this.rideService.exportRidesCSV(
      operatorId,
      statusId,
      undefined,
      routeId,
      startTime,
      endTime,
      customerId
    ).pipe(
      takeUntil(this.subject),
      catchError((err) => {
        this.notificationsAlerts.openSnackBar('Помилка експорту CSV', 'Ok');
        return of('');
      })
    ).subscribe((csvData) => {
      if (csvData) {
        this.downloadCSVFile(csvData, `rides_${new Date().toISOString().split('T')[0]}.csv`);
        this.notificationsAlerts.openSnackBar('CSV файл успішно завантажено', 'Ok');
      }
    });
  }

  private downloadCSVFile(data: string, fileName: string): void {
    const blob = new Blob([data], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', fileName);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  onStatusChange(element: Ride, status: RideStatus): void {
    if (status.name.toLowerCase() === 'cancelled') {
      const dialogRef = this.dialog.open(CancelReasonDialogComponent, {
        width: '400px'
      });

      dialogRef.afterClosed().subscribe(result => {
        if (result) {
          this.updateRideStatus(element, status, result.reason, result.delivered_by_car, result.cancel_reason_id);
        }
      });
    } else {
      this.updateRideStatus(element, status);
    }
  }


  private updateRideStatus(element: Ride, statusObj: RideStatus, cancelReason?: string, deliveredByCar?: boolean, cancelReasonId?: number): void {
    element.updating = true;

    const updatedRide: any = {
      ride_status_id: statusObj.id
    };

    if (cancelReason) {
      updatedRide.cancel_reason = cancelReason;
    }

    if (cancelReasonId) {
      updatedRide.cancel_reason_id = cancelReasonId;
    }

    if (deliveredByCar !== undefined) {
      updatedRide.delivered_by_car = deliveredByCar;
    }

    this.rideService.updateRide(element.id, updatedRide).subscribe(
      (response) => {
        element.updating = false;
        element.ride_status_id = statusObj.id;
        element.ride_status = statusObj;
        if (cancelReason) {
          element.cancel_reason = cancelReason;
        }
        if (deliveredByCar !== undefined) {
          element.delivered_by_car = deliveredByCar;
        }
        this.dataSource.data = [...this.data];
        this.notificationsAlerts.openSnackBar('Status updated successfully', 'Ok');
      },
      () => {
        element.updating = false;
        this.notificationsAlerts.openSnackBar('Error updating status', 'Ok');
      }
    );
  }
}
