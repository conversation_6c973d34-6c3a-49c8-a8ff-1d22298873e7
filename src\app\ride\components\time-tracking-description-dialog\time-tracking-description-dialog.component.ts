import { Component, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { FormControl, Validators } from '@angular/forms';

export interface TimeTrackingDescriptionDialogData {
  rideId: number;
  routeId: number;
}

@Component({
  selector: 'app-time-tracking-description-dialog',
  templateUrl: './time-tracking-description-dialog.component.html',
  styleUrls: ['./time-tracking-description-dialog.component.css']
})
export class TimeTrackingDescriptionDialogComponent {
  descriptionControl = new FormControl('', [Validators.maxLength(255)]);
  
  constructor(
    public dialogRef: MatDialogRef<TimeTrackingDescriptionDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: TimeTrackingDescriptionDialogData
  ) { }

  onCancel(): void {
    this.dialogRef.close();
  }

  onSubmit(): void {
    if (this.descriptionControl.valid) {
      this.dialogRef.close({
        description: this.descriptionControl.value,
        rideId: this.data.rideId,
        routeId: this.data.routeId
      });
    }
  }
}
