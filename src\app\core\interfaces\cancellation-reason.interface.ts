export interface CancellationReasonDto {
  id: number;
  name: string;
  description: string;
}
export interface CancellationReason {
  id: string;
  name: string;
  category: 'Environmental' | 'Customer' | 'Technical' | 'Operations';
  color: string;
}


export const CATEGORY_MAPPING = {
  'Environmental': {
    keywords: ['environmental'],
    color: '#ff9800'
  },
  'Customer': {
    keywords: ['customer', 'cancelled by customer'],
    color: '#2196f3'
  },
  'Technical': {
    keywords: ['technical'],
    color: '#f44336'
  },
  'Operations': {
    keywords: ['operations'],
    color: '#9e9e9e'
  }
};



export const CANCELLATION_REASONS: CancellationReason[] = [
  {
    id: 'env-advance',
    name: 'Environmental - 12h in advance',
    category: 'Environmental',
    color: '#ff9800'
  },
  {
    id: 'env-surprise',
    name: 'Environmental - Surprise',
    category: 'Environmental',
    color: '#ff5722'
  },
  {
    id: 'ops-team',
    name: 'Operations Team',
    category: 'Operations',
    color: '#9e9e9e'
  },
  {
    id: 'cust-advance',
    name: 'Customer - 24h in advance',
    category: 'Customer',
    color: '#2196f3'
  },
  {
    id: 'cust-short',
    name: 'Customer - short notice',
    category: 'Customer',
    color: '#3f51b5'
  },
  {
    id: 'tech-issue',
    name: 'Technical Issue',
    category: 'Technical',
    color: '#f44336'
  }
];
