<div class="mat-elevation-z2 sub-content layout-style">
  <div class="tab-content">
    <div class="d-flex">

      <!-- Status Filter -->
      <mat-select placeholder="Glider" class="form-control input-style me-3" appearance="standard" floatLabel="never"
                  [formControl]="gliderFilterFormControl">
        <mat-option *ngFor="let item of gliderFilter" [value]="item.id">
          {{ item.name }}
        </mat-option>
      </mat-select>

      <!-- Search Filter -->
      <mat-form-field class="form-control input-style me-3" appearance="standard" floatLabel="never">
        <mat-icon matPrefix>search</mat-icon>
        <input placeholder="Search" matInput [formControl]="searchFieldFormControl">
      </mat-form-field>

      <button mat-raised-button class="btn-green btn-style mr-3" matTooltip="Clear all the filters"
              matTooltipClass="basic-tooltip" (click)="clearfilters()">
        Clear
      </button>

      <button mat-raised-button class="btn-green btn-style mr-3" matTooltip="Add Task" matTooltipClass="basic-tooltip"
              (click)="openDialogCreate()">
        New Maintenance
      </button>
    </div>
    <br>
    <div class="table-container" *ngIf="dataSource.filteredData.length != 0">
      <table mat-table [dataSource]="dataSource" class="mt-1 collapse-separate" style="table-layout: fixed; width: 100%;">

        <!-- Glider Column -->
        <ng-container matColumnDef="glider">
          <th mat-header-cell *matHeaderCellDef class="headerText text-center" style="width: 10%">
            Glider
            <mat-icon class="actions-only-icons icon-white mlr align-mid" matTooltipClass="basic-tooltip"
                      matTooltip="Sort" (click)="orderByObject('glider', 'name')">
              <span class="material-icons">
                import_export_icon
              </span>
            </mat-icon>
          </th>
          <td mat-cell *matCellDef="let element" class="text-center">
            <span>{{ element.glider?.name }}</span>
          </td>
        </ng-container>

        <!-- Due Date Column -->
        <ng-container matColumnDef="due_date">
          <th mat-header-cell *matHeaderCellDef class="headerText text-center">
            Due Date
            <mat-icon class="actions-only-icons icon-white mlr align-mid" matTooltipClass="basic-tooltip"
                      matTooltip="Sort" (click)="orderBy('dueDateTime')">
              <span class="material-icons">
                import_export_icon
              </span>
            </mat-icon>
          </th>
          <td mat-cell *matCellDef="let element" class="text-center">
            <span>{{ element.dueDateTime | date: 'dd/MM/yyyy' }}</span>
          </td>
        </ng-container>

        <!-- Completed Date Column -->
        <ng-container matColumnDef="completed_date">
          <th mat-header-cell *matHeaderCellDef class="headerText text-center">
            Completed Date
            <mat-icon class="actions-only-icons icon-white mlr align-mid" matTooltipClass="basic-tooltip"
                      matTooltip="Sort" (click)="orderBy('completedDateTime')">
              <span class="material-icons">
                import_export_icon
              </span>
            </mat-icon>
          </th>
          <td mat-cell *matCellDef="let element" class="text-center">
            <span>{{ element.completedDateTime | date: 'dd/MM/yyyy' }}</span>
          </td>
        </ng-container>

        <!-- Scheduled Date Column -->
        <ng-container matColumnDef="type">
          <th mat-header-cell *matHeaderCellDef class="headerText text-center">Type</th>
          <td mat-cell *matCellDef="let element" class="text-center">
            {{ element.maintenanceType.name }}
          </td>
        </ng-container>

        <ng-container matColumnDef="notes">
          <th mat-header-cell  *matHeaderCellDef class="headerText text-center" style="width: 35%;">
            Notes
          </th>
          <td mat-cell *matCellDef="let element"  class="text-center truncated-cell">
            <span>{{ element.notes }}</span>
          </td>
        </ng-container>
        <ng-container matColumnDef="post_check">
          <th mat-header-cell *matHeaderCellDef class="headerText text-center" style="width: 8%">
            Post Check
          </th>
          <td mat-cell *matCellDef="let element" class="text-center">
            <mat-icon *ngIf="element.postMaintenanceChecklistDone" style="color: #66b245;">check</mat-icon>
          </td>
        </ng-container>
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef class="headerText text-center" style="width: 8%;">
            Actions
          </th>
          <td mat-cell *matCellDef="let element" class="text-center">
            <button mat-icon-button color="primary" (click)="openDialogEdit(element)">
              <mat-icon>edit</mat-icon>
            </button>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns" class="headerTable"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>
    </div>

    <div class="text-center bold mt-3" *ngIf="dataSource.filteredData.length == 0 && this.dataSource.filter">
      <img src="../../../../assets/images/undraw_floating_re_xtcj.svg" alt="not found">
      <h2 mat-dialog-title>
        No maintenances found
      </h2>
    </div>

    <mat-paginator [pageSize]="20" [pageSizeOptions]="[10, 20, 50, 100]" showFirstLastButtons></mat-paginator>
  </div>
</div>
