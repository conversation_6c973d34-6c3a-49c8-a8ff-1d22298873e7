
export interface UserData {
  id: number;
  user_role_id?: number | null;
  email: string;
  password_digest?: string | null;
  first_name: string;
  last_name: string;
  company_name?: string | null;
  phone_country?: string | null;
  phone_number?: string | null;
  country?: string | null;
  address_line1?: string | null;
  address_line2?: string | null;
  city?: string | null;
  zip_code: string | null;
  vat?: string | null;
  picture_url?: string | null;
  profile_picture?: string | null;
  balance?: string | null;
  display_currency?: string | null;
  referral_code?: string | null;
  referral_code_used?: string | null;
  referral_code_used_id?: string | null;
  jedsetter_status_id: number | null;
  employed_by?: UserData | null;
  employed_by_id?: number | null;
  time_zone?: string;
  employee_count?: number;
  facebook_id?: string;
  google_id?: string;
  created_at: string;
  updated_at: string;
}

