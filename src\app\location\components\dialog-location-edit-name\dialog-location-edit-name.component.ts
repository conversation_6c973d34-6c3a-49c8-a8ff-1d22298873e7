import { Component, OnInit, Inject, OnD<PERSON>roy } from '@angular/core';
import { Subject, of } from 'rxjs';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { catchError, takeUntil } from 'rxjs/operators';

import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { Location } from '@app/core/interfaces/location.interface';
import { LocationService } from '@app/services/location/location.service';
import { NotificationsService } from '@shared/services/notifications.service';
import { typeAction } from '@app/shared/utils/enum';

@Component({
  selector: 'app-dialog-location-edit-name',
  templateUrl: './dialog-location-edit-name.component.html',
  styleUrls: ['./dialog-location-edit-name.component.css'],
})
export class DialogLocationEditNameComponent implements OnInit, OnDestroy {
  form!: FormGroup;
  subject = new Subject<any>();

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: Location,
    private locationService: LocationService,
    private notificationsAlerts: NotificationsService,
    public dialogRef: MatDialogRef<DialogLocationEditNameComponent>
  ) {
    this.form = new FormGroup({
      name: new FormControl(this.data.name, [Validators.required]),
    });
  }

  ngOnInit(): void {}

  update(): void {
    if (!this.form.invalid) {
      let body = {
        location: {
          name: this.form.value.name,
        },
      };

      this.locationService
        .updateLocation(this.data.id, body)
        .pipe(
          takeUntil(this.subject),
          catchError((err) => {
            this.notificationsAlerts.openSnackBar('Error', 'Ok');
            return of(err);
          })
        )
        .subscribe((res) => {
          if (res.status) {
            this.locationService.notifyDataSubject(
              res.data,
              typeAction.updated
            );
            this.dialogRef.close();
          }
          this.notificationsAlerts.openSnackBar(res.message, 'Ok');
        });
    }
  }

  get formDialog(): any {
    return this.form.controls;
  }

  ngOnDestroy(): void {
    this.subject.complete();
    this.subject.unsubscribe();
  }
}
