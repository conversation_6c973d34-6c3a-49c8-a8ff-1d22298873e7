import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DialogRideComponent } from './dialog-ride.component';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { AuthModule } from '@auth/auth.module';
import { RideService } from '@app/services/ride/ride.service';

describe('DialogRideComponent', () => {
  let component: DialogRideComponent;
  let fixture: ComponentFixture<DialogRideComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ DialogRideComponent ],
      providers: [
        {
          provide: MAT_DIALOG_DATA,
          useValue: {},
        },
        {
          provide: MatDialogRef,
          useValue: {},
        },
        RideService
      ],
      imports: [AuthModule]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DialogRideComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
