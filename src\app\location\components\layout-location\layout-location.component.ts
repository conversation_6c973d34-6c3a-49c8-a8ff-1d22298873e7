import { Component, OnInit } from '@angular/core';
import { Location } from '@app/core/interfaces/location.interface';
import { LocationService } from '@app/services/location/location.service';
import { NotificationsService } from '@app/shared/services/notifications.service';
import { SharedVarService } from '@services/SharedVarService/shared-var-service.service';
import { of, Subject } from 'rxjs';
import { catchError, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-layout-location',
  templateUrl: './layout-location.component.html',
  styleUrls: ['./layout-location.component.css'],
})
export class LayoutLocationComponent implements OnInit {
  flagLocationsAccepted = true;
  flagLocationsRequest = true;
  subject = new Subject<any>();
  data: Location[] = [];
  dataActive: Location[] = [];
  dataRequest: Location[] = [];

  constructor(
    private sharedVarService: SharedVarService,
    private locationService: LocationService,
    private notificationsAlerts: NotificationsService
  ) {}

  ngOnInit(): void {
    this.sharedVarService.setValue('Locations');
    this.loadData();
  }

  loadData(): void {
    this.locationService
      .getLocationsList()
      .pipe(
        takeUntil(this.subject),
        catchError((err) => {
          this.notificationsAlerts.openSnackBar('Error', 'Ok');
          return of(err);
        })
      )
      .subscribe((res) => {
        this.data = res;
        this.subject.next(this.data);
      });
  }

  eventFlagAccepted(e: any): void {
    this.flagLocationsAccepted = e;
  }

  eventFlagRequest(e: any): void {
    this.flagLocationsRequest = e;
  }

  ngOnDestroy(): void {
    this.subject.complete();
    this.subject.unsubscribe();
  }
}
