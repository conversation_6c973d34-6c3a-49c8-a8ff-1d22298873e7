import { Location } from '@app/core/interfaces/location.interface';

export const LOCATIONS: Location[] = [
  {
    id: 2,
    name: '<PERSON>',
    location_category: {
      id: 2,
      category: 'landingzone',
      comment: null,
      created_at: '2022-04-01T09:15:03.229Z',
      updated_at: '2022-04-01T09:15:03.229Z',
    },
    gps_latitude: 77362890,
    gps_longitude: -62215728,
    gps_altitude: 254,
    heading: 156,
    picture_url: 'http://langosh-gut<PERSON>.info/timmy',
    video_url: 'http://torp-gibson.biz/marisol_mante',
    owner: {
      id: 2,
      user_role_id: 1,
      email: 'nor<PERSON>_s<PERSON><PERSON>@kling-kiehn.co',
      password_digest:
        '$2a$12$tH6AuPJNe/pVpfejLL6PT.gzfikNlVmvN5esLVFbOdGXq6G1t/GtO',
      first_name: '<PERSON><PERSON>',
      last_name: '<PERSON>ya<PERSON>',
      phone_country: null,
      phone_number: null,
      country: null,
      address_line1: '243 Hershel Pass',
      address_line2: null,
      city: null,
      zip_code: '48214',
      vat: null,
      picture_url: null,
      balance: null,
      display_currency: null,
      referral_code: null,
      referral_code_used_id: null,
      employed_by_id: null,
      created_at: '2022-04-01T09:15:06.172Z',
      updated_at: '2022-04-01T09:15:06.172Z',
    },
    location_status: {
      id: 1,
      status: 'available',
      comment: null,
      created_at: '2022-04-01T09:15:03.167Z',
      updated_at: '2022-04-01T09:15:03.167Z',
    },
    ready_eta: '2022-04-01 09:15:08 +0000',
    tag: 2,
    node: {
      id: 1,
      name: 'Diego Rivera',
      gps_latitude: 59074932,
      gps_longitude: -102696670,
      gps_altitude: 606,
      owner_id: 10,
      created_at: '2022-04-01T09:15:08.319Z',
      updated_at: '2022-04-01T09:15:08.319Z',
    },
    created_at: '2022-04-01T09:15:08.497Z',
    updated_at: '2022-04-01T09:15:08.497Z',
  },
];
