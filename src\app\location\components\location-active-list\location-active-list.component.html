<div class="tab-content">
  <div class="d-flex">

    <!-- Category Filter -->
    <mat-select placeholder="Category" class="form-control input-style me-3" appearance="standard" floatLabel="never" [formControl]="categoryFilter">
      <mat-option *ngFor="let item of filterCategory" [value]="item.name">
          {{item.name}}
      </mat-option>
    </mat-select>

    <!-- Status Filter -->
    <mat-select placeholder="Status" class="form-control input-style me-3" appearance="standard" floatLabel="never" [formControl]="statusFilter">
      <mat-option *ngFor="let item of filterStatus" [value]="item.name">
          {{item.name}}
      </mat-option>
    </mat-select>

    <!-- Search Filter -->
    <mat-form-field class="form-control input-style me-3" appearance="standard" floatLabel="never">
      <mat-icon matPrefix>search</mat-icon>
      <input placeholder="Search" type="text" matInput [formControl]="nameFilter">
    </mat-form-field>

    <button mat-raised-button class="btn-green btn-style mr-3" matTooltip="Clear all the filters" matTooltipClass="basic-tooltip" (click)="clearfilters()">
      Clear
    </button>
  </div>
  <br>
  <div class="table-container" *ngIf="dataSource.filteredData.length != 0">
    <table mat-table [dataSource]="dataSource" class="mt-1 collapse-separate">
      

      <!-- Name Column -->
      <ng-container matColumnDef="name">
        <th mat-header-cell *matHeaderCellDef class="headerText text-center">
          Name
          <mat-icon class="actions-only-icons icon-white mlr align-mid" matTooltipClass="basic-tooltip" matTooltip="Sort" (click)="orderBy('name')">
            <span class="material-icons">
              import_export_icon
            </span>
          </mat-icon>
        </th>
        <td mat-cell *matCellDef="let element" class="btn-detail text-center">
          <span (click)="openDialogEdit(element)">{{element.name}}</span>
        </td>
      </ng-container>

      <!-- Category Column -->
      <ng-container matColumnDef="category">
        <th mat-header-cell *matHeaderCellDef class="headerText text-center">
          Category
          <mat-icon class="actions-only-icons icon-white mlr align-mid" matTooltipClass="basic-tooltip" matTooltip="Sort" (click)="orderByObject('location_category','category')">
            <span class="material-icons">
              import_export_icon
            </span>
          </mat-icon>
        </th>
        <td mat-cell *matCellDef="let element" class="text-center"> {{element.locationCategory.name}} </td>
      </ng-container>

      <!-- Gps coordinates Column -->
     

     

      <!-- Status Column -->
      <ng-container matColumnDef="status">
        <th mat-header-cell *matHeaderCellDef class="headerText text-center">
          Status
          <mat-icon class="actions-only-icons icon-white mlr align-mid" matTooltipClass="basic-tooltip" matTooltip="Sort" (click)="orderByObject('location_status','status')">
            <span class="material-icons">
              import_export_icon
            </span>
          </mat-icon>
        </th>
        <td mat-cell *matCellDef="let element" class="text-center">
            <div class="d-flex flex-column bd-highlight">
                <div class="bd-highlight d-flex justify-content-center">
                    <mat-chip-list aria-label="Fish selection">
                        <mat-chip [ngClass]="{
                            'btn-green': element.locationStatus.name == status.available,
                            'btn-gray': element.locationStatus.name == status.docked,
                            'btn-orange': element.locationStatus.name == status.charging,
                            'btn-green-olivine': element.locationStatus.name == status.awaitingDelivery,
                            'btn-yellow': element.locationStatus.name == status.emergency,
                            'btn-sapphire': element.locationStatus.name == status.reqSupport,
                            'btn-purple': element.locationStatus.name == status.requested,
                            'btn-red': element.locationStatus.name == status.denied
                        }" selected>
                            {{element.locationStatus.name}}</mat-chip>
                    </mat-chip-list>
                </div>
            </div>
        </td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns" class="headerTable"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>
  </div>

  <div class="text-center bold mt-3" *ngIf="dataSource.filteredData.length == 0 && this.dataSource.filter">
    <img src="../../../../assets/images/undraw_floating_re_xtcj.svg" alt="not found">
    <h2 mat-dialog-title>
      No locations found
    </h2>
  </div>

  <mat-paginator [pageSize]="20" [pageSizeOptions]="[10, 20, 50, 100]" showFirstLastButtons></mat-paginator>
</div>