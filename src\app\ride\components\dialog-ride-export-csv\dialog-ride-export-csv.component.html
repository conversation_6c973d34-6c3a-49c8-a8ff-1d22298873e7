<mat-dialog-content>
  <h2 mat-dialog-title>
    Export Rides CSV
    <span mat-button mat-dialog-close class="float-right">
      <mat-icon class="actions-only-icons">
        <span class="material-icons icon-green">
          close
        </span>
      </mat-icon>
    </span>
</h2>
<h4 mat-dialog-title>
  You can optionally specify filters below
</h4>
  <form [formGroup]="form">

    <div class="row mt-3">
      <div class="col-6">
        <label>
          Start Date
        <input formControlName="start_date" class="form-control mt-2" [valueAsDate]="data.start_date" name="start_date" type="date" id="start_date"/>
        </label>
      </div>
      <div class="col-6">
        <label>
          End Date
          <input formControlName="end_date" class="form-control mt-2" [valueAsDate]="data.end_date" name="end_date" type="date" id="end_date"/>
        </label>
      </div>
    </div>
    
    <div class="row mt-3">
      <label class="w-100">
        Glider
        <br>
        <mat-form-field class="w-100 mt-2 mat-autocomplete" appearance="outline" floatLabel="never">
          <mat-icon matPrefix>search</mat-icon>
          <input type="text" placeholder="Search by name or ID" matInput formControlName="glider" [matAutocomplete]="auto" id="glider" name="glider">
          <mat-autocomplete autoActiveFirstOption #auto="matAutocomplete" [displayWith]="displayFn">
            <mat-option *ngFor="let item of filteredOptions | async" [value]="item">
              {{item.name}}
            </mat-option>
          </mat-autocomplete>

          </mat-form-field>
        </label>
  </div>

  <div class="row mt-3">
    <label class="w-100">
      Departure Mailbox
      <br>
      <mat-form-field class="w-100 mt-2 mat-autocomplete" appearance="outline" floatLabel="never">
        <mat-icon matPrefix>search</mat-icon>
        <input type="text" placeholder="Search by name or ID" matInput formControlName="start_location" [matAutocomplete]="autoStartLocation" id="start_location" name="start_location">
        <mat-autocomplete autoActiveFirstOption #autoStartLocation="matAutocomplete" [displayWith]="displayFnLocation">
          <mat-option *ngFor="let item of filteredStartLocationOptions | async" [value]="item">
            {{item.name}}
          </mat-option>
        </mat-autocomplete>
        </mat-form-field>
      </label>
</div>

<div class="row mt-3">
  <label class="w-100">
    Destination Mailbox
    <br>
    <mat-form-field class="w-100 mt-2 mat-autocomplete" appearance="outline" floatLabel="never">
      <mat-icon matPrefix>search</mat-icon>
      <input type="text" placeholder="Search by name or ID" matInput formControlName="destination_location" [matAutocomplete]="autoDestinationLocation" id="destination_location" name="destination_location">
      <mat-autocomplete autoActiveFirstOption #autoDestinationLocation="matAutocomplete" [displayWith]="displayFnLocation">
        <mat-option *ngFor="let item of filteredDestinationLocationOptions | async" [value]="item">
          {{item.name}}
        </mat-option>
      </mat-autocomplete>
      </mat-form-field>
    </label>
</div>

<div class="row">
  <div class="col">
    <label>
      <mat-checkbox class="mt-2" formControlName="include_package" [checked]="true"></mat-checkbox>
      Include Rides WITH package
    </label>
  </div>
</div>

<div class="row">
  <div class="col">
    <label>
      <mat-checkbox class="mt-2" formControlName="include_no_package" [checked]="true"></mat-checkbox>
      Include Rides WITHOUT packages
    </label>
  </div>
</div>

  <mat-dialog-actions align="end" class="mb-5 mt-1">
    <button mat-button cdkFocusInitial class="btn-green" (click)="create()" type="button">Export CSV</button>
  </mat-dialog-actions>
</form>
</mat-dialog-content>