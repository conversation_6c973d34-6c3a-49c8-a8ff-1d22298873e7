<div class="mat-elevation-z2 sub-content layout-style">
  <div class="tab-content">
    <div class="d-flex">

      <!-- Status Filter -->
      <mat-select placeholder="Status" class="form-control input-style me-3" appearance="standard" floatLabel="never"
                  [formControl]="statusFilter">
        <mat-option *ngFor="let item of filterStatus" [value]="item.name">
          {{ item.name }}
        </mat-option>
      </mat-select>

      <!-- Search Filter -->
      <mat-form-field class="form-control input-style me-3" appearance="standard" floatLabel="never">
        <mat-icon matPrefix>search</mat-icon>
        <input placeholder="Search by name" type="text" matInput [formControl]="nameFilter">
      </mat-form-field>

      <button mat-raised-button class="btn-green btn-style mr-3" matTooltip="Clear all the filters"
              matTooltipClass="basic-tooltip" (click)="clearfilters()">
        Clear
      </button>

      <!-- Hollow Button -->
      <button mat-raised-button class="btn-green-mountain btn-style mr-3" matTooltip="Register a new Glider"
              matTooltipClass="basic-tooltip">
        <a href={{urlNewbornGliderWard}} target="_blank" class="text-white">
          Register a new glider
        </a>
      </button>
    </div>
    <br>
    <div class="table-container" *ngIf="dataSource.filteredData.length != 0">
      <table mat-table [dataSource]="dataSource" class="mt-1 collapse-separate">


        <!-- Name Column -->
        <ng-container matColumnDef="name">
          <th mat-header-cell *matHeaderCellDef class="headerText text-center">
            Name
            <mat-icon class="actions-only-icons icon-white mlr align-mid" matTooltipClass="basic-tooltip"
                      matTooltip="Sort" (click)="orderBy('name')">
              <span class="material-icons">
                import_export_icon
              </span>
            </mat-icon>
          </th>
          <td mat-cell *matCellDef="let element" class="btn-detail text-center">
            <span [routerLink]="['/fleets', 'fleet-details', element.id]">{{ element.name }}</span>
          </td>
        </ng-container>

        <!-- IP Column -->
        <ng-container matColumnDef="ip">
          <th mat-header-cell *matHeaderCellDef class="headerText text-center">
            IP
            <mat-icon class="actions-only-icons icon-white mlr align-mid" matTooltipClass="basic-tooltip"
                      matTooltip="Sort" (click)="orderBy('vpnIp')">
              <span class="material-icons">
                import_export_icon
              </span>
            </mat-icon>
          </th>
          <td mat-cell *matCellDef="let element" class="text-center">
            <span>{{ element.vpnIp }}</span>
          </td>
        </ng-container>


        <!-- Status Column -->
        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef class="headerText text-center">
            Status
            <mat-icon class="actions-only-icons icon-white mlr align-mid" matTooltipClass="basic-tooltip"
                      matTooltip="Sort" (click)="orderByObject('status', 'name')">
              <span class="material-icons">
                import_export_icon
              </span>
            </mat-icon>
          </th>
          <td mat-cell *matCellDef="let element" class="text-center">
            <div class="d-flex flex-column bd-highlight">
              <div class="bd-highlight d-flex justify-content-center">
                <mat-chip-list aria-label="Status selection">
                  <mat-chip *ngIf="element.gliderStatus?.name"
                            class="status-chip"
                            [ngStyle]="{'background-color': element.gliderStatus.colorHexcode ? '#' + element.gliderStatus.colorHexcode : null}">
                    {{ element.gliderStatus.name }}
                  </mat-chip>
                </mat-chip-list>
              </div>
            </div>
          </td>

        </ng-container>

        <!-- Company Column -->
        <ng-container matColumnDef="company">
          <th mat-header-cell *matHeaderCellDef class="headerText text-center">
            Company
            <mat-icon class="actions-only-icons icon-white mlr align-mid" matTooltipClass="basic-tooltip"
                      matTooltip="Sort" (click)="orderByObject('company', 'name')">
              <span class="material-icons">
                import_export_icon
              </span>
            </mat-icon>
          </th>
          <td mat-cell *matCellDef="let element" class="text-center">
            <span>{{ element.company?.name }}</span>
          </td>
        </ng-container>

        <!-- Region Column -->
        <ng-container matColumnDef="region">
          <th mat-header-cell *matHeaderCellDef class="headerText text-center">
            Region
            <mat-icon class="actions-only-icons icon-white mlr align-mid" matTooltipClass="basic-tooltip"
                      matTooltip="Sort" (click)="orderByObject('region', 'country')">
              <span class="material-icons">
                import_export_icon
              </span>
            </mat-icon>
          </th>
          <td mat-cell *matCellDef="let element" class="text-center">
            <span>{{ element.region?.country }}</span>
          </td>
        </ng-container>


        <!-- Manufacturing Date Column -->
        <ng-container matColumnDef="manufacturing_date">
          <th mat-header-cell *matHeaderCellDef class="headerText text-center">
            Manufacturing Date
            <mat-icon class="actions-only-icons icon-white mlr align-mid" matTooltipClass="basic-tooltip"
                      matTooltip="Sort" (click)="orderBy('manufacturingDate')">
              <span class="material-icons">
                import_export_icon
              </span>
            </mat-icon>
          </th>
          <td mat-cell *matCellDef="let element"
              class="text-center"> {{ element.manufacturingDate | date:'dd/MM/yyyy' }}
          </td>
        </ng-container>

        <!-- Pixhawk Column -->
        <ng-container matColumnDef="pixhawk">
          <th mat-header-cell *matHeaderCellDef class="headerText text-center">
            Pixhawk
          </th>
          <td mat-cell *matCellDef="let element" class="text-center">
            <span>{{ element.pixhawkUuid }}</span>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns" class="headerTable"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>
    </div>

    <div class="text-center bold mt-3" *ngIf="dataSource.filteredData.length == 0 && this.dataSource.filter">
      <img src="../../../../assets/images/undraw_floating_re_xtcj.svg" alt="not found">
      <h2 mat-dialog-title>
        No fleets found
      </h2>
    </div>

    <mat-paginator [pageSize]="20" [pageSizeOptions]="[10, 20, 50, 100]" showFirstLastButtons></mat-paginator>
  </div>
</div>
