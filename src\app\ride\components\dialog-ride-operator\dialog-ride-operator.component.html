<mat-dialog-content>
    <h2 mat-dialog-title>
        Assign to Operator {{ data.id }}
        <span mat-button mat-dialog-close class="float-right">
            <mat-icon class="actions-only-icons">
                <span class="material-icons icon-green">
                    close
                </span>
            </mat-icon>
        </span>
    </h2>
    <form [formGroup]="form" (submit)="update()">
        <div class="row mt-3">
            <label class="w-100">
                Flights ID
                <input formControlName="ride_id" class="form-control mt-2 input-style-form" type="text"
                    [attr.disabled]="true">
            </label>
        </div>
        <div class="row mt-3">
            <label class="w-100">
                Operator
                <br>
                <mat-form-field class="w-100 mt-2 mat-autocomplete" appearance="outline" floatLabel="never">
                    <mat-icon matPrefix>search</mat-icon>
                    <input type="text" placeholder="Search by name or ID" matInput formControlName="user"
                        [matAutocomplete]="auto" required>
                    <mat-autocomplete autoActiveFirstOption #auto="matAutocomplete" [displayWith]="displayFn">
                        <mat-option *ngFor="let item of filteredOptions | async" [value]="item">
                            <span *ngIf="item.company_name; else name">
                                {{item.company_name}}
                            </span>
                            <ng-template #name>
                                {{item.email}}
                            </ng-template>
                        </mat-option>
                    </mat-autocomplete>
                    <mat-error *ngIf="this.dataUser.length == 0">No records found</mat-error>
                </mat-form-field>
            </label>
        </div>
        <mat-dialog-actions align="end">
            <button mat-button mat-dialog-close (click)="cancel()" class="btn-cancel">Cancel</button>
            <button mat-button cdkFocusInitial class="btn-green" type="submit" [disabled]="!form.valid">Assign</button>
        </mat-dialog-actions>
        <br>
    </form>
</mat-dialog-content>