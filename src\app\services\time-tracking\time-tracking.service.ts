import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Observable, from, throwError } from 'rxjs';
import { switchMap, catchError } from 'rxjs/operators';
import { environment } from '@src/environments/environment';
import { KeycloakService } from 'keycloak-angular';

@Injectable({
  providedIn: 'root'
})
export class TimeTrackingService {
  private apiUrl = environment.urlMsRides;

  constructor(
    private http: HttpClient,
    private keycloakService: KeycloakService
  ) { }

  startTimeTracking(
    routeId: number,
    pilotEmail: string,
    description?: string,
    rideId?: number
  ): Observable<any> {

    return from(this.keycloakService.getToken()).pipe(
      switchMap((token) => {
        const headers = new HttpHeaders({
          Authorization: `Bearer ${token}`,
        });

        let params = new HttpParams()
          .set('route_id', routeId.toString())
          .set('pilot_email', pilotEmail);

        if (description) params = params.set('description', description);
        if (rideId) params = params.set('ride_id', rideId.toString());


        return this.http.post(
          `${this.apiUrl}/time-tracking/start`,
          null,
          { headers, params }
        ).pipe(
          catchError(error => {
            console.error('Time tracking request failed:', error);
            throw error;
          })
        );
      })
    );
  }

  stopTimeTracking(
    shiftId: number
  ): Observable<any> {
    console.log('Stopping time tracking with shift ID:', shiftId);

    return from(this.keycloakService.getToken()).pipe(
      switchMap((token) => {
        const headers = new HttpHeaders({
          Authorization: `Bearer ${token}`
        });

        let params = new HttpParams()
          .set('shift_id', shiftId.toString());


        return this.http.post(
          `${this.apiUrl}/time-tracking/stop`,
          null,
          { headers, params }
        ).pipe(
          catchError(error => {
            console.error('Stop time tracking request failed:', error);
            throw error;
          })
        );
      })
    );
  }

  editTimeTracking(
    shiftId: number,
    editInfo: {
      pilot_email?: string;
      route_id?: number;
      start_time?: string;
      stop_time?: string;
      description?: string;
      ride_id?: number;
    }
  ): Observable<any> {
    return from(this.keycloakService.getToken()).pipe(
      switchMap((token) => {
        const headers = new HttpHeaders({
          Authorization: `Bearer ${token}`
        });

        let params = new HttpParams()
          .set('shift_id', shiftId.toString());

        return this.http.post(
          `${this.apiUrl}/time-tracking/edit`,
          editInfo,
          { headers, params }
        );
      })
    );
  }

  getShiftEvents(
    startDate?: string,
    endDate?: string,
    pilotEmail?: string,
    routeId?: number,
    rideId?: number,
    skip: number = 0,
    limit: number = 10
  ): Observable<any> {
    return from(this.keycloakService.getToken()).pipe(
      switchMap((token) => {
        const headers = new HttpHeaders({
          Authorization: `Bearer ${token}`
        });

        let params = new HttpParams()
          .set('skip', skip.toString())
          .set('limit', limit.toString());

        if (startDate) params = params.set('start_date', startDate);
        if (endDate) params = params.set('end_date', endDate);
        if (pilotEmail) params = params.set('pilot_email', pilotEmail);
        if (routeId) params = params.set('route_id', routeId.toString());
        if (rideId) params = params.set('ride_id', rideId.toString());

        return this.http.get(
          `${this.apiUrl}/shifts`,
          { headers, params }
        );
      })
    );
  }

  getLastShift(pilotEmail: string): Observable<any> {

    return from(this.keycloakService.getToken()).pipe(
      switchMap((token) => {
        const headers = new HttpHeaders({
          Authorization: `Bearer ${token}`
        });

        let params = new HttpParams()
          .set('pilot_email', pilotEmail);


        return this.http.get(
          `${this.apiUrl}/shifts/last`,
          { headers, params }
        ).pipe(
          catchError(error => {
            console.error('Last shift request failed:', error);
            throw error;
          })
        );
      })
    );
  }
}
