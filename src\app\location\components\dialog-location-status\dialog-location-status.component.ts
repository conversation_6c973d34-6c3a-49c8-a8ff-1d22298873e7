import { Component, OnInit, Inject } from '@angular/core';
import { Subject, of, Observable } from 'rxjs';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { catchError, map, startWith, takeUntil } from 'rxjs/operators';

import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { NotificationsService } from '@shared/services/notifications.service';
import { typeAction } from '@app/shared/utils/enum';
import {
  Location,
  LocationStatus,
} from '@app/core/interfaces/location.interface';
import { LocationService } from '@app/services/location/location.service';

@Component({
  selector: 'app-dialog-location-status',
  templateUrl: './dialog-location-status.component.html',
  styleUrls: ['./dialog-location-status.component.css'],
})
export class DialogLocationStatusComponent implements OnInit {
  form!: FormGroup;
  subject = new Subject<any>();
  filteredOptions: Observable<LocationStatus[]>;

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: Location,
    @Inject(MAT_DIALOG_DATA) public dataStatus: LocationStatus[],
    private locationService: LocationService,
    private notificationsAlerts: NotificationsService,
    public dialogRef: MatDialogRef<DialogLocationStatusComponent>
  ) {}

  ngOnInit(): void {
    this.form = new FormGroup({
      location_id: new FormControl(this.data.id, [Validators.required]),
      status: new FormControl(this.data.locationStatus, [Validators.required]),
    });

    this.loadData();
  }

  loadData(): void {
    this.locationService
      .getLocationsStatusList()
      .pipe(
        takeUntil(this.subject),
        catchError((err) => {
          this.notificationsAlerts.openSnackBar('Error', 'Ok');
          return of(err);
        })
      )
      .subscribe((res) => {
        this.dataStatus = res.data;

        this.filteredOptions = this.form.controls.status.valueChanges.pipe(
          startWith(''),
          map((value) => {
            const note =
              typeof value === 'string' ? value : value?.status || value?.id;
            return note
              ? this._filter(note as string)
              : this.dataStatus.slice();
          })
        );
      });
  }

  displayFn(status: LocationStatus): string {
    return status && status.name ? status.name : '';
  }

  private _filter(value: string): LocationStatus[] {
    const filterValue = value.toLowerCase();

    return this.dataStatus.filter(
      (status) =>
        status.name.toLowerCase().includes(filterValue) ||
        status.id.toString().includes(filterValue)
    );
  }

  accept(): void {
    this.dialogRef.close(true);
  }

  cancel(): void {
    this.dialogRef.close(false);
  }

  update(): void {
    if (!this.form.invalid && this.form.value.status.id) {
      const body = {
        location: {
          location_status_id: this.form.value.status.id,
        },
      };

      this.locationService
        .updateLocation(this.data.id, body)
        .pipe(
          catchError((err) => {
            this.notificationsAlerts.openSnackBar('Error', 'Ok');
            return of(err);
          })
        )
        .subscribe((res) => {
          if (res.status) {
            this.locationService.notifyDataSubject(
              res.data,
              typeAction.updated
            );
            this.dialogRef.close();
          }
          this.notificationsAlerts.openSnackBar(res.message, 'Ok');
        });
    }
  }

  get formDialog(): any {
    return this.form.controls;
  }

  ngOnDestroy(): void {
    this.subject.complete();
    this.subject.unsubscribe();
  }
}
