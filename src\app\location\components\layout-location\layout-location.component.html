<!--<mat-progress-bar *ngIf="flagLocationsAccepted || flagLocationsRequest" mode="indeterminate"></mat-progress-bar>-->
<div class="mat-elevation-z2 sub-content sub-content-up layout-style">
    <app-location-active-list (eventData)="eventFlagAccepted($event)"></app-location-active-list>
    <!-- <mat-tab-group class="custom-mat-tab"> -->
        <!-- <mat-tab>
            <ng-template mat-tab-label>
               <b>Active</b>
            </ng-template>
        </mat-tab>

        <mat-tab>
            <ng-template mat-tab-label>
                <b>Requests</b>
            </ng-template>
            <app-location-request-list (eventData)="eventFlagRequest($event)"></app-location-request-list>
        </mat-tab> -->
    <!-- </mat-tab-group> -->
</div>
