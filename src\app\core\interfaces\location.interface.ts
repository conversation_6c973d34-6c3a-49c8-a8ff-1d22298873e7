import { UserData } from './userdata.interface';

export interface LocationCategory {
  id: number;
  createdAt: string;
  updatedAt: string;
  name: string;
  description: string;
}

export interface LocationStatus {
  id: number;
  createdAt: string;
  updatedAt: string;
  name: string;
  description: string;
}

export interface Location {
  id: number;
  name: string;
  gpsLat: number;
  gpsLong: number;
  gpsAlt: number;
  pictureUrl: string;
  videoUrl: string;
  locationCategory: LocationCategory;
  locationStatus: LocationStatus;
  createdAt: string;
  updatedAt: string;
}


