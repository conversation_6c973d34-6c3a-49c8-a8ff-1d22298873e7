import {Component, Inject, OnInit} from '@angular/core';
import {Form<PERSON>uilder, FormGroup, Validators} from '@angular/forms';
import {MAT_DIALOG_DATA, MatDialogRef} from '@angular/material/dialog';
import {SharedVarService} from '@services/SharedVarService/shared-var-service.service';


@Component({
  selector: 'app-request-maintenance-dialog',
  templateUrl: './request-maintenance-dialog.component.html',
  styleUrls: ['./request-maintenance-dialog.component.css']
})
export class RequestMaintenanceDialogComponent implements OnInit {
  form!: FormGroup;

  reasonOptions: string[] = ['Incident', 'Software Update', 'Other'];

  constructor(
    private sharedVar: SharedVarService,
    private fb: FormBuilder,
    private dialogRef: MatDialogRef<RequestMaintenanceDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
  }

  userEmail = '';

  ngOnInit() {
    this.form = this.fb.group({
      maintenanceReason: ['Incident', Validators.required],
      extraNotes: ['']
    });

    this.sharedVar.getStateValue<string>('userEmail').subscribe((email) => {
      if (email) {
        this.userEmail = email;
        console.log('Retrieved email:', this.userEmail);
      }
    });
  }

  onSubmit() {
    if (this.form.valid) {
      const payload = {
        glider_name: this.data.gliderName,
        user_email: this.userEmail,
        maintenance_reasons: this.form.value.maintenanceReason,
        extra_notes: this.form.value.extraNotes
      };

      this.dialogRef.close(payload);
    }
  }

  onCancel() {
    this.dialogRef.close(null);
  }
}
