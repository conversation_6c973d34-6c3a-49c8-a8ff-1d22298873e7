<h2 mat-dialog-title>Edit Location {{ data.id }}</h2>
<mat-dialog-content>
  <form [formGroup]="form" (submit)="update()">
    <div class="row">
      <div class="col">
        <label class="w-100">
          Location Name
          <input formControlName="name" class="form-control mt-2" type="text" required>
          <mat-error *ngIf="formDialog.name?.errors?.required && formDialog.name.touched">Name is
            required</mat-error>
        </label>
      </div>
    </div>
    
    <mat-dialog-actions align="end" class="mt-3">
      <button mat-button mat-dialog-close class="btn-cancel">Cancel</button>
      <button mat-button cdkFocusInitial class="btn-green" type="submit" [disabled]="!form.valid">Save</button>
    </mat-dialog-actions>
    <br>
  </form>
</mat-dialog-content>