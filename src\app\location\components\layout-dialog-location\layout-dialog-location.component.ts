import { Component, OnInit, Inject } from '@angular/core';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Location } from '@app/core/interfaces/location.interface';
import { LOCATION_REQUEST_STATUS } from '@app/location/mock/status';

import { SharedVarService } from '@services/SharedVarService/shared-var-service.service';
import { Subject } from 'rxjs';

@Component({
  selector: 'app-layout-dialog-location',
  templateUrl: './layout-dialog-location.component.html',
  styleUrls: ['./layout-dialog-location.component.css'],
})
export class LayoutDialogLocationComponent implements OnInit {
  flagLocationsGeneral = true;
  flagLocationsUsers = true;
  subject = new Subject<any>();
  requestStatus = LOCATION_REQUEST_STATUS;

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: Location,
    private sharedVarService: SharedVarService
  ) {}

  ngOnInit(): void {
    this.subject.next(this.data);
  }

  eventFlagGeneral(e: any): void {
    this.flagLocationsGeneral = e;
  }

  eventFlagUsers(e: any): void {
    this.flagLocationsUsers = e;
  }
}
