export interface SoftwareVersion {
  id: number;
  name: string;
  createdAt: string;
  updatedAt: string;
  softwareVersionType: SoftwareVersionType;
}

export interface SoftwareVersionType {
  id: number;
  name: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateSoftwareVersionDto {
  name: string;
  createdAt: string;
  updatedAt: string;
  softwareVersionTypeId: number;
}

export interface CreateSoftwareVersionTypeDto {
  name: string;
  createdAt: string;
  updatedAt: string;
}
