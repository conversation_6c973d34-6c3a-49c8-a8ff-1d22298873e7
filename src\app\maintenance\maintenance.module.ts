import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MAT_DIALOG_DEFAULT_OPTIONS } from '@angular/material/dialog';
import { MaterialModule } from '@app/material/material.module';
import { GliderMaintenanceService } from '@app/services/gliderMaintenance/gliderMaintenance.service';
import { SharedModule } from '@app/shared/shared.module';
import { DialogMaintenanceCreateComponent } from './components/dialog-maintenance-create/dialog-maintenance-create.component';
import { MaintenanceListComponent } from './components/maintenance-list/maintenance-list.component';
import { MaintenanceRoutingModule } from './maintenance-routing.module';
import { FleetService } from '@app/services/fleet/fleet.service';

@NgModule({
  declarations: [MaintenanceListComponent, DialogMaintenanceCreateComponent],
  imports: [
    CommonModule,
    MaintenanceRoutingModule,
    MaterialModule,
    SharedModule,
    ReactiveFormsModule,
  ],
  providers: [
    {
      provide: MAT_DIALOG_DEFAULT_OPTIONS,
      useValue: { hasBackdrop: true },
    },
    GliderMaintenanceService,
    FleetService
  ],
})
export class MaintenanceModule {}
