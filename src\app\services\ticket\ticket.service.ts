import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Response } from '@app/core/interfaces/responses/response.interface';
import {
  Ticket,
  TicketCategory,
  TicketStatus,
} from '@app/core/interfaces/ticket.interface';
import { typeAction } from '@app/shared/utils/enum';
import { environment } from '@src/environments/environment';
import { Observable, Subject } from 'rxjs';

@Injectable()
export class TicketService {
  private apiUrl = environment.urlMsTickets;
  private apiCategoryUrl = environment.urlMsTicketsCategories;
  private apiStatusUrl = environment.urlMsTicketsStatus;
  private subjectTickets = new Subject<Ticket[]>();
  public action: typeAction | undefined;

  constructor(private http: HttpClient) {}

  getTicketList(): Observable<Response<Ticket[]>> {
    return this.http.get<Response<Ticket[]>>(`${this.apiUrl}.json`);
  }

  getTicketCategoriesList(): Observable<Response<TicketCategory[]>> {
    return this.http.get<Response<TicketCategory[]>>(
      `${this.apiCategoryUrl}.json`
    );
  }

  getTicketStatusList(): Observable<Response<TicketStatus[]>> {
    return this.http.get<Response<TicketStatus[]>>(`${this.apiStatusUrl}.json`);
  }

  updateTicket(ticketId: number, ticket: any): Observable<Response<Ticket>> {
    return this.http.patch<Response<Ticket>>(
      `${this.apiUrl}/${ticketId}.json`,
      ticket
    );
  }

  notifyDataSubject(status: Ticket[], action?: typeAction): void {
    this.action = action;
    this.subjectTickets.next(status);
  }

  listenDataSubjectTicket(): Observable<Ticket[]> {
    return this.subjectTickets.asObservable();
  }
}
