import { Pipe, PipeTransform } from '@angular/core';
import { formatDate } from '@angular/common';

@Pipe({
  name: 'epochToDate'
})
export class EpochToDatePipe implements PipeTransform {
  transform(epochSeconds: number | null | undefined, dateFormat: string = 'medium'): string {
    if (epochSeconds == null) {
      return '';
    }

    if (typeof epochSeconds !== 'number') {
      console.warn(`Invalid epochSeconds value: ${epochSeconds}`);
      return '';
    }

    const dateObj = new Date(epochSeconds * 1000);

    if (isNaN(dateObj.getTime())) {
      console.warn(`Unable to convert epochSeconds to date: ${epochSeconds}`);
      return '';
    }

    return formatDate(dateObj, dateFormat, 'en-US');
  }
}
