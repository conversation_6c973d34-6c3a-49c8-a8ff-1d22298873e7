import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { RideTiming } from '@app/core/interfaces/ride-timing.interface';
import { RideTimingService } from '@app/services/ride-timing/ride-timing.service';
import { FormControl, Validators } from '@angular/forms';
import { NotificationsService } from '@shared/services/notifications.service';

@Component({
  selector: 'app-ride-timing-history',
  templateUrl: './ride-timing-history.component.html',
  styleUrls: ['./ride-timing-history.component.css']
})
export class RideTimingHistoryComponent implements OnInit {
  @Input() rideId: number;
  @Input() rideTimings: RideTiming[] = [];
  @Output() close = new EventEmitter<void>();

  editingIndex: number | null = null;
  editingField: string | null = null;
  editTimeControl = new FormControl('');
  editStartTimeControl = new FormControl('');
  editEndTimeControl = new FormControl('');
  editDescriptionControl = new FormControl('', [Validators.maxLength(255)]);

  constructor(
    private rideTimingService: RideTimingService,
    private notificationsService: NotificationsService
  ) { }

  ngOnInit(): void {
  }

  formatTimestamp(timestamp: string): string {
    return this.rideTimingService.formatTimestamp(timestamp);
  }

  formatTimestampToTime(timestamp: string): string {
    return this.rideTimingService.formatTimestampToTime(timestamp);
  }

  formatMilliseconds(ms: number): string {
    return this.rideTimingService.formatMilliseconds(ms);
  }

  startEditing(index: number, field: string): void {
    this.cancelEditing();

    this.editingIndex = index;
    this.editingField = field;
    const timing = this.rideTimings[index];

    switch (field) {
      case 'duration':
        this.editTimeControl.setValue(this.formatMilliseconds(timing.ms));
        break;
      case 'timeperiod':
        const startDate = new Date(timing.startTimestamp);
        const endDate = new Date(timing.endTimestamp);

        const startFormatted = startDate.toISOString().slice(0, 19);
        const endFormatted = endDate.toISOString().slice(0, 19);

        this.editStartTimeControl.setValue(startFormatted);
        this.editEndTimeControl.setValue(endFormatted);
        break;
      case 'description':
        this.editDescriptionControl.setValue(timing.description || '');
        break;
    }
  }

  saveCurrentEditing(): void {
    if (this.editingIndex === null || !this.editingField) { return; }

    switch (this.editingField) {
      case 'duration':
        this.saveEditing();
        break;
      case 'timeperiod':
        this.saveTimeperiodEditing();
        break;
      case 'description':
        this.saveDescriptionEditing();
        break;
    }
  }

  saveEditing(): void {
    if (this.editingIndex === null || this.editingField !== 'duration') { return; }

    const timeValue = this.editTimeControl.value;

    let match = timeValue.match(/^(\d+):(\d+):(\d+),(\d+)$/);
    if (!match) {
      match = timeValue.match(/^(\d+):(\d+):(\d+)$/);
    }
    if (!match) {
      match = timeValue.match(/^(\d+):(\d+)$/);
    }

    if (match) {
      let hoursStr = '0', minutesStr = '0', secondsStr = '0', tenthsStr = '0';

      if (match.length === 5) {
        [, hoursStr, minutesStr, secondsStr, tenthsStr] = match;
      } else if (match.length === 4) {
        [, hoursStr, minutesStr, secondsStr] = match;
      } else if (match.length === 3) {
        [, minutesStr, secondsStr] = match;
      }

      const totalMs =
        parseInt(hoursStr) * 3600000 +
        parseInt(minutesStr) * 60000 +
        parseInt(secondsStr) * 1000 +
        parseInt(tenthsStr || '0') * 100;

      const index = this.editingIndex!;
      const timing = this.rideTimings[index];
      this.rideTimingService.updateTiming(this.rideId, index, totalMs);
      this.notificationsService.openSnackBar('Duration updated successfully', 'Ok');
      this.cancelEditing();
    } else {
      this.notificationsService.openSnackBar('Invalid time format. Use HH:MM:SS or HH:MM:SS,T', 'Ok');
    }
  }

  saveTimeperiodEditing(): void {
    if (this.editingIndex === null || this.editingField !== 'timeperiod') { return; }

    const startTime = this.editStartTimeControl.value;
    const endTime = this.editEndTimeControl.value;

    if (!startTime || !endTime) {
      this.notificationsService.openSnackBar('Both start and end times are required', 'Ok');
      return;
    }

    const startDate = new Date(startTime);
    const endDate = new Date(endTime);

    if (startDate >= endDate) {
      this.notificationsService.openSnackBar('End time must be after start time', 'Ok');
      return;
    }

    const index = this.editingIndex!;
    const timing = this.rideTimings[index];
    const newMs = endDate.getTime() - startDate.getTime();

    if (timing.fromServer && timing.shiftId) {
      this.rideTimingService.timeTrackingService.editTimeTracking(
        timing.shiftId,
        {
          start_time: startDate.toISOString(),
          stop_time: endDate.toISOString()
        }
      ).subscribe(
        response => {
          this.notificationsService.openSnackBar('Time period updated on server', 'Ok');

          this.rideTimings[index] = {
            ...timing,
            startTimestamp: startDate.toISOString(),
            endTimestamp: endDate.toISOString(),
            ms: newMs,
            edited: true
          };

          this.cancelEditing();
        },
        error => {
          this.notificationsService.openSnackBar('Error updating time period', 'Ok');
        }
      );
    } else {
      this.rideTimings[index] = {
        ...timing,
        startTimestamp: startDate.toISOString(),
        endTimestamp: endDate.toISOString(),
        ms: newMs,
        edited: true
      };

      this.rideTimingService.saveRideTimingsToStorage();
      this.notificationsService.openSnackBar('Time period updated', 'Ok');
      this.cancelEditing();
    }
  }

  saveDescriptionEditing(): void {
    if (this.editingIndex === null || this.editingField !== 'description') { return; }
    const index = this.editingIndex!;
    const description = this.editDescriptionControl.value;
    const timing = this.rideTimings[index];

    if (timing.fromServer && timing.shiftId) {
      this.rideTimingService.timeTrackingService.editTimeTracking(
        timing.shiftId,
        {
          description: description
        }
      ).subscribe(
        response => {
          this.notificationsService.openSnackBar('Description updated on server', 'Ok');

          this.rideTimings[index] = {
            ...timing,
            description: description,
            edited: true
          };

          this.cancelEditing();
        },
        error => {
          this.notificationsService.openSnackBar('Error updating description', 'Ok');
        }
      );
    } else {
      this.rideTimings[index] = {
        ...timing,
        description: description,
        edited: true
      };

      this.rideTimingService.saveRideTimingsToStorage();
      this.notificationsService.openSnackBar('Description updated', 'Ok');
      this.cancelEditing();
    }
  }

  cancelEditing(): void {
    this.editingIndex = null;
    this.editingField = null;
    this.editTimeControl.reset();
    this.editStartTimeControl.reset();
    this.editEndTimeControl.reset();
    this.editDescriptionControl.reset();
  }

  getTotalTime(): number {
    return this.rideTimings.reduce((total, timing) => total + timing.ms, 0);
  }

  getFormattedTotalTime(): string {
    return this.formatMilliseconds(this.getTotalTime());
  }

  onClose(): void {
    this.close.emit();
  }
}
