<div class="mat-elevation-z2 sub-content layout-style">
  <div class="tab-content">
    <div class="filters-container mb-3">
      <div class="filters-row">
        <div class="filter-item">
          <mat-form-field appearance="outline">
            <mat-label>Status</mat-label>
            <mat-select [formControl]="statusFilter">
              <mat-option *ngFor="let item of filterStatus" [value]="item.name">
                {{ item.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <div class="filter-item">
          <mat-form-field appearance="outline">
            <mat-label>Operators</mat-label>
            <mat-select [formControl]="operatorFilter">
              <mat-option value="">All operators</mat-option>
              <div class="operator-search-container">
                <div class="operator-search-input-container">
                  <mat-icon class="operator-search-icon">search</mat-icon>
                  <input class="operator-search-input" type="text" placeholder="Search operators" [formControl]="operatorSearchControl" (click)="$event.stopPropagation()">
                </div>
              </div>
              <mat-option *ngFor="let user of filteredUsers" [value]="user.id">
                {{ user.email || user.first_name + ' ' + user.last_name }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <div class="filter-item">
          <mat-form-field appearance="outline">
            <mat-label>Routes</mat-label>
            <mat-select [formControl]="routeFilter">
              <mat-option value="">All routes</mat-option>
              <mat-option *ngFor="let route of routes" [value]="route.id">
                {{ route.start_location_name }} → {{ route.end_location_name }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>

        <div class="filter-item">
          <mat-form-field appearance="outline">
            <mat-label>Customers</mat-label>
            <mat-select [formControl]="customerFilter">
              <mat-option value="">All customers</mat-option>
              <mat-option *ngFor="let customer of customers" [value]="customer.id">
                {{ customer.name }}
              </mat-option>
            </mat-select>
          </mat-form-field>
        </div>


      </div>

      <div class="filters-row">
        <div class="filter-item">
          <mat-form-field appearance="outline">
            <mat-label>Start Date & Time</mat-label>
            <input matInput
                   type="datetime-local"
                   [formControl]="departureTimeFromFilter">
            <button *ngIf="departureTimeFromFilter.value"
                    matSuffix
                    mat-icon-button
                    aria-label="Clear"
                    (click)="departureTimeFromFilter.setValue(null); $event.stopPropagation();">
              <mat-icon>close</mat-icon>
            </button>
          </mat-form-field>
        </div>

        <div class="filter-item">
          <mat-form-field appearance="outline">
            <mat-label>End Date & Time</mat-label>
            <input matInput
                   type="datetime-local"
                   [formControl]="departureTimeToFilter">
            <button *ngIf="departureTimeToFilter.value"
                    matSuffix
                    mat-icon-button
                    aria-label="Clear"
                    (click)="departureTimeToFilter.setValue(null); $event.stopPropagation();">
              <mat-icon>close</mat-icon>
            </button>
          </mat-form-field>
        </div>



        <div class="filter-actions">
          <button mat-raised-button class="btn-green btn-style clear-button" (click)="clearfilters()" matTooltip="Clear all filters">
            <mat-icon>clear_all</mat-icon>
            Clear
          </button>

          <button mat-raised-button class="btn-style export-button" (click)="exportRidesToCSV()" matTooltip="Export to CSV">
            <mat-icon>file_download</mat-icon>
            Export
          </button>
        </div>
      </div>
    </div>
    <br>
    <div class="table-container" *ngIf="isLoading">
      <div class="skeleton-row" *ngFor="let i of [1,2,3,4,5,6,7,8,9,10]">
        <div class="skeleton-cell skeleton-cell-sm skeleton-loader"></div>
        <div class="skeleton-cell skeleton-cell-sm skeleton-loader"></div>
        <div class="skeleton-cell skeleton-cell-md skeleton-loader"></div>
        <div class="skeleton-cell skeleton-cell-md skeleton-loader"></div>
        <div class="skeleton-cell skeleton-cell-lg skeleton-loader"></div>
        <div class="skeleton-cell skeleton-cell-lg skeleton-loader"></div>
        <div class="skeleton-cell skeleton-cell-sm skeleton-loader"></div>
        <div class="skeleton-cell skeleton-cell-md skeleton-loader"></div>
        <div class="skeleton-cell skeleton-cell-md skeleton-loader"></div>
        <div class="skeleton-cell skeleton-cell-lg skeleton-loader"></div>
        <div class="skeleton-cell skeleton-cell-sm skeleton-loader"></div>
      </div>
    </div>

    <div class="table-container" *ngIf="dataSource.filteredData.length != 0 && !isLoading">
      <table mat-table [dataSource]="dataSource" class="mt-1 collapse-separate">

        <!-- Ride Glider Column -->
        <ng-container matColumnDef="glider">
          <th mat-header-cell *matHeaderCellDef class="headerText text-center header-cell">
            <div class="header-content">
              <span>Drone</span>
            </div>
          </th>
          <td mat-cell *matCellDef="let element" class="text-center">
            <div class="d-flex align-items-center justify-content-center">
              <mat-icon *ngIf="isGliderWarningStatus(element.glider_status)"
                       [ngClass]="getGliderWarningIconClass(element.glider_status)"
                       class="warning-icon mr-1"
                       [matTooltip]="getGliderStatusTooltip(element.glider_status)"
                       matTooltipPosition="above">warning</mat-icon>
              <span class="btn-detail" [routerLink]="['/fleets', 'fleet-details', element.glider_id]">{{ element.glider_name }}</span>
            </div>
          </td>
        </ng-container>

        <!-- Operator Column -->
        <ng-container matColumnDef="operator">
          <th mat-header-cell *matHeaderCellDef class="headerText text-center header-cell">
            <div class="header-content">
              <span>Operator</span>
            </div>
          </th>
          <td mat-cell *matCellDef="let element" class="text-center">
            <span>{{ element.operator_email }}</span>
          </td>
        </ng-container>


        <!-- Location Departure Column -->
        <ng-container matColumnDef="location_departure">
          <th mat-header-cell *matHeaderCellDef class="headerText text-center header-cell">
            <div class="header-content">
              <span>Location departure</span>
            </div>
          </th>
          <td mat-cell *matCellDef="let element" class="text-center">
            <span>{{ element.from_location_name }}</span>
          </td>
        </ng-container>

        <!-- Location Arrival Column -->
        <ng-container matColumnDef="location_arrival">
          <th mat-header-cell *matHeaderCellDef class="headerText text-center header-cell">
            <div class="header-content">
              <span>Location arrival</span>
            </div>
          </th>
          <td mat-cell *matCellDef="let element" class="text-center">
            <span>{{ element.to_location_name }}</span>
          </td>
        </ng-container>

        <!-- Time Departure Column -->
        <ng-container matColumnDef="time_departure">
          <th mat-header-cell *matHeaderCellDef class="headerText text-center header-cell">
            <div class="header-content">
              <span>Time departure</span>
            </div>
          </th>
          <td mat-cell *matCellDef="let element" class="text-center"> {{ element.departure_time | date:'HH:mm:ss' }}</td>
        </ng-container>

        <!-- Date Departure Column -->
        <ng-container matColumnDef="date_departure">
          <th mat-header-cell *matHeaderCellDef class="headerText text-center header-cell">
            <div class="header-content">
              <span>Date departure</span>
            </div>
          </th>
          <td mat-cell *matCellDef="let element" class="text-center"> {{ element.departure_time | date:'yy/MM/dd' }}</td>
        </ng-container>

        <!-- Time arrival Column -->
        <ng-container matColumnDef="time_arrival">
          <th mat-header-cell *matHeaderCellDef class="headerText text-center header-cell">
            <div class="header-content">
              <span>Time arrival</span>
            </div>
          </th>
          <td mat-cell *matCellDef="let element" class="text-center"> {{ element.arrival_time | date:'HH:mm:ss' }}</td>
        </ng-container>

        <!-- Status Column -->
        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef class="headerText text-center header-cell">
            <div class="header-content">
              <span>Status</span>
            </div>
          </th>
          <td mat-cell *matCellDef="let element" class="text-center">
            <div class="d-flex align-items-center justify-content-center">
              <div
                class="status-chip status-filled d-inline-flex align-items-center"
                [ngStyle]="{'background-color': getStatusBadgeColor(element.ride_status, element.cancel_reason)}"
                [matMenuTriggerFor]="statusMenu"
                [matTooltip]="getStatusDescription(element.ride_status_id, element.cancel_reason)"
                matTooltipPosition="above"
                matTooltipClass="status-tooltip"
              >
                <span class="status-text">{{ getFormattedStatusText(element.ride_status, element.cancel_reason) }}</span>
                <mat-icon>arrow_drop_down</mat-icon>
              </div>
              <mat-menu #statusMenu="matMenu">
                <ng-container *ngFor="let status of filterStatus">
                  <button mat-menu-item (click)="onStatusChange(element, status)">
                    {{ status.name }}
                  </button>
                </ng-container>
              </mat-menu>
            </div>
          </td>
        </ng-container>

        <!-- Reason Column -->
        <ng-container matColumnDef="reason">
          <th mat-header-cell *matHeaderCellDef class="headerText text-center header-cell">
            <div class="header-content">
              <span>Reason</span>
            </div>
          </th>
          <td mat-cell *matCellDef="let element" class="text-center">
            <span *ngIf="getFormattedStatusText(element.ride_status, element.cancel_reason).includes('Cancelled')" class="truncated-text" [matTooltip]="getReasonText(element.cancel_reason)">{{ getReasonText(element.cancel_reason) }}</span>
          </td>
        </ng-container>

        <!-- Timetracking Column -->
        <ng-container matColumnDef="timetracking" sticky>
          <th mat-header-cell *matHeaderCellDef class="headerText text-center header-cell">
            <div class="header-content">
              <span>Time Tracking</span>
            </div>
          </th>
          <td mat-cell *matCellDef="let element" class="text-center">
            <div class="timetracking-container">
              <div class="timer-display" [class.timer-running]="isTimerRunning(element.id)">
                <mat-icon class="timer-status-icon">{{ isTimerRunning(element.id) ? 'timer' : 'timer_off' }}</mat-icon>
                <span class="timer-value">{{ getFormattedTime(element.id) }}</span>
              </div>
              <div class="timer-controls">
                <button mat-icon-button class="timer-button" *ngIf="!isTimerRunning(element.id)" (click)="startTimer(element.id)" matTooltip="Start timer">
                  <mat-icon class="timer-icon">play_arrow</mat-icon>
                </button>
                <button mat-icon-button class="timer-button stop-button" *ngIf="isTimerRunning(element.id)" (click)="stopTimer(element.id)" matTooltip="Stop timer">
                  <mat-icon class="timer-icon">stop</mat-icon>
                </button>
                <button mat-icon-button class="timer-button history-button" (click)="showTimingHistory(element.id)" matTooltip="View history">
                  <mat-icon class="timer-icon">history</mat-icon>
                </button>
              </div>
            </div>
          </td>
        </ng-container>

        <!-- Action Column -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef class="headerText text-center header-cell">
            <div class="header-content">
              <span>Actions</span>
            </div>
          </th>
          <td mat-cell *matCellDef="let element" class="text-center d-flex align-items-center justify-content-center">
            <div class="d-flex flex-column bd-highlight">
              <div class="bd-highlight d-flex justify-content-center mt-3">
                <button mat-button [matMenuTriggerFor]="menuNotification">
                  <mat-icon matTooltipClass="basic-tooltip" matTooltip="Actions">
                    <span class="material-icons">
                      more_horiz
                    </span>
                  </mat-icon>
                </button>
                <mat-menu #menuNotification="matMenu">
                  <button mat-menu-item (click)="openDialogOperator(element)">Assign to Operator</button>
                  <button mat-menu-item (click)="openDialogGlider(element)">Assign to Glider</button>
                  <button mat-menu-item (click)="openDialogEditRides(element)">Edit ride</button>
                  <button mat-menu-item *ngIf="isAdmin()" (click)="deleteRide(element)" class="delete-action">
                    <mat-icon color="warn">delete</mat-icon>
                    <span class="delete-text">Delete ride</span>
                  </button>
                </mat-menu>
              </div>
            </div>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns" class="headerTable"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>
    </div>

    <div class="text-center bold mt-3" *ngIf="data.length == 0 && !isLoading">
      <img src="../../../../assets/images/undraw_floating_re_xtcj.svg" alt="not found">
      <h2 mat-dialog-title>
        No rides found
      </h2>
    </div>

    <mat-paginator
      [pageSize]="pageSize"
      [pageSizeOptions]="[10, 20, 50, 100]"
      [length]="totalRides"
      [pageIndex]="pageIndex"
      (page)="onPageChange($event)"
      showFirstLastButtons
      *ngIf="!isLoading">
    </mat-paginator>
  </div>


  <div *ngIf="selectedRideForHistory !== null" class="timing-history-modal">
    <div class="timing-history-backdrop" (click)="hideTimingHistory()"></div>
    <div class="timing-history-content">
      <app-ride-timing-history
        [rideId]="selectedRideForHistory"
        [rideTimings]="getTimingsForRide(selectedRideForHistory)"
        (close)="hideTimingHistory()">
      </app-ride-timing-history>
    </div>
  </div>
</div>
