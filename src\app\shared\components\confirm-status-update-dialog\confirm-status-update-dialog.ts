import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';

export interface ConfirmStatusDialogData {
  newStatus: string;
}

@Component({
  selector: 'app-confirm-status-update-dialog',
  templateUrl: './confirm-status-update-dialog.html',
  styleUrls: ['./confirm-status-update-dialog.css']
})
export class ConfirmStatusUpdateDialogComponent implements OnInit {
  note: string = '';

  constructor(
    public dialogRef: MatDialogRef<ConfirmStatusUpdateDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: ConfirmStatusDialogData
  ) {}

  ngOnInit(): void {
  }

  onConfirm(): void {
    this.dialogRef.close(this.note);
  }

  onCancel(): void {
    this.dialogRef.close();
  }
}
