import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DialogRideExportCSVComponent } from './dialog-ride-export-csv.component';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { AuthModule } from '@auth/auth.module';
import { RideService } from '@app/services/ride/ride.service';

describe('DialogRideExportCSVComponent', () => {
  let component: DialogRideExportCSVComponent;
  let fixture: ComponentFixture<DialogRideExportCSVComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ DialogRideExportCSVComponent ],
      providers: [
        {
          provide: MAT_DIALOG_DATA,
          useValue: {},
        },
        {
          provide: MatDialogRef,
          useValue: {},
        },
        RideService
      ],
      imports: [AuthModule]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DialogRideExportCSVComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
