import {CommonModule} from '@angular/common';
import {NgModule} from '@angular/core';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import {MAT_DIALOG_DEFAULT_OPTIONS} from '@angular/material/dialog';
import {MaterialModule} from '@app/material/material.module';
import {FleetService} from '@app/services/fleet/fleet.service';
import {SharedModule} from '@app/shared/shared.module';
import {FleetDetailsComponent} from './components/fleet-details/fleet-details.component';
import {FleetListComponent} from './components/fleet-list/fleet-list.component';
import {FleetRoutingModule} from './fleet-routing.module';
import {GliderMaintenanceService} from '@app/services/gliderMaintenance/gliderMaintenance.service';
import {MatProgressSpinnerModule} from '@angular/material/progress-spinner';
import {
  RequestMaintenanceDialogComponent
} from '@app/fleet/components/fleet-details/maintenance-dialog/request-maintenance-dialog.component';
import {MatRadioModule} from '@angular/material/radio';
import {MatButtonModule} from '@angular/material/button';
import {MatInputModule} from '@angular/material/input';
import {EpochToDatePipe} from '@shared/utils/epoch-to-date.pipe';
import {SecondsToHmsPipe} from '@shared/utils/seconds-to-hms.pipe';
import {MatSliderModule} from '@angular/material/slider';
import {MatSlideToggleModule} from '@angular/material/slide-toggle';
import {MatIconModule} from '@angular/material/icon';
import {MatSnackBarModule} from '@angular/material/snack-bar';
import {MatFormFieldModule} from '@angular/material/form-field';
import {MatSelectModule} from '@angular/material/select';
import {MatTooltipModule} from '@angular/material/tooltip';
import {MatDividerModule} from '@angular/material/divider';
import {BrowserAnimationsModule} from '@angular/platform-browser/animations';
import {MatDialogModule} from '@angular/material/dialog';
import {ConfirmSoftwareVersionDialogComponent} from '@shared/components/dialog-update-sw-version/update-sw-versions-dialog.component';
import {SecondsToTimePipe} from '@shared/utils/seconds-to-time.pipe';
import {
  ConfirmStatusUpdateDialogComponent
} from '@shared/components/confirm-status-update-dialog/confirm-status-update-dialog';
import {MatCardModule} from '@angular/material/card';
import {FormattedFlightTimePipe} from '@shared/utils/formatted-flight-time.pipe';

@NgModule({
  declarations: [FleetListComponent, FleetDetailsComponent, RequestMaintenanceDialogComponent, EpochToDatePipe,
    ConfirmSoftwareVersionDialogComponent, SecondsToHmsPipe, SecondsToTimePipe, ConfirmStatusUpdateDialogComponent, FormattedFlightTimePipe
  ],
  imports: [
    CommonModule,
    FleetRoutingModule,
    MaterialModule,
    SharedModule,
    ReactiveFormsModule,
    MatProgressSpinnerModule,
    MatRadioModule,
    MatSlideToggleModule,
    MatDialogModule,
    MatButtonModule,
    MatInputModule,
    FormsModule,
    MatSlideToggleModule,
    MatIconModule,
    MatSnackBarModule,
    MatFormFieldModule,
    MatSelectModule,
    ReactiveFormsModule,
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatDialogModule,
    MatDividerModule,
    MatCardModule
  ],
  providers: [
    GliderMaintenanceService,
    {
      provide: MAT_DIALOG_DEFAULT_OPTIONS,
      useValue: {hasBackdrop: true},
    },
    FleetService,
  ],
})
export class FleetModule {
}
