<mat-dialog-content>
  <form [formGroup]="form">
    <div class="row mt-3">
      <div class="col-4">
        <label>
          Ride ID
          <input formControlName="id" class="form-control mt-2 input-style-form" type="text" [attr.disabled]="true">
        </label>
      </div>
      <div class="col-4">
        <label class="cursor-pointer" (click)="openDialogOperator()">
          Operator
          <input formControlName="operator" class="form-control mt-2 input-style-form cursor-pointer" type="text"
            [attr.disabled]="true">
        </label>
      </div>
      <div class="col-4">
        <label>
          Category
          <input formControlName="category" class="form-control mt-2 input-style-form" type="text"
            [attr.disabled]="true">
        </label>
      </div>
    </div>

    <div class="row mt-3">
      <div class="col-6">
        <label>
          Location departure
          <input formControlName="location_departure" class="form-control mt-2 input-style-form" type="text"
            [attr.disabled]="true">
        </label>
      </div>
      <div class="col-6">
        <label>
          Location arrival
          <input formControlName="location_arrival" class="form-control mt-2 input-style-form" type="text"
            [attr.disabled]="true">
        </label>
      </div>
    </div>

    <div class="row mt-3">
      <div class="col-6">
        <label>
          Time departure
          <input formControlName="time_departure" class="form-control mt-2 input-style-form" type="text"
            [attr.disabled]="true">
        </label>
      </div>
      <div class="col-6">
        <label>
          Time arrival
          <input formControlName="time_arrival" class="form-control mt-2 input-style-form" type="text"
            [attr.disabled]="true">
        </label>
      </div>
    </div>

    <div class="row mt-3">
      <div class="col-4">
        <label>
          Status
          <input formControlName="status" class="form-control mt-2 input-style-form" type="text" [attr.disabled]="true">
        </label>
      </div>
      <div class="col-4">
        <label class="cursor-pointer" (click)="openDialogGlider()">
          Glider
          <input formControlName="glider_id" class="form-control mt-2 input-style-form cursor-pointer" type="text"
            [attr.disabled]="true">
        </label>
      </div>
      <div class="col-4">
        <label class="cursor-pointer" (click)="openDialogGcs()">
          GSC
          <input formControlName="gsc_id" class="form-control mt-2 input-style-form cursor-pointer" type="text"
            (click)="openDialogGcs()" [attr.disabled]="true">
        </label>
      </div>
    </div>

    <div class="row mt-3">
      <div class="col-6">
        <label>
          Scheduled
          <input formControlName="scheduled" class="form-control mt-2 input-style-form" type="text"
            [attr.disabled]="true">
        </label>
      </div>
      <div class="col-6">
        <label>
          Price
          <input formControlName="price" class="form-control mt-2 input-style-form" type="text" [attr.disabled]="true">
        </label>
      </div>
    </div>

    <br>
  </form>
</mat-dialog-content>