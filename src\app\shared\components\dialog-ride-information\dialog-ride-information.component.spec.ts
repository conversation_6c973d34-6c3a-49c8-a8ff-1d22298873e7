import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DialogRideInfoComponent } from './dialog-ride-information.component';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { AuthModule } from '@auth/auth.module';
import { RideService } from '@app/services/ride/ride.service';

describe('DialogRideInfoComponent', () => {
  let component: DialogRideInfoComponent;
  let fixture: ComponentFixture<DialogRideInfoComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DialogRideInfoComponent],
      providers: [
        {
          provide: MAT_DIALOG_DATA,
          useValue: {},
        },
        {
          provide: MatDialogRef,
          useValue: {},
        },
        RideService,
      ],
      imports: [AuthModule],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DialogRideInfoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
