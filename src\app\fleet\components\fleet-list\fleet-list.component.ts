import {
  Component,
  OnInit,
  AfterViewInit,
  ViewChild,
  OnDestroy,
  Output,
  EventEmitter,
  ViewEncapsulation,
} from '@angular/core';
import {of, Subject} from 'rxjs';

import {MatPaginator} from '@angular/material/paginator';
import {MatTableDataSource} from '@angular/material/table';
import {MatDialog} from '@angular/material/dialog';

import {NotificationsService} from '@shared/services/notifications.service';

import {Fleet} from '@app/core/interfaces/fleet.interface';
import {FleetService} from '@app/services/fleet/fleet.service';
import {catchError, takeUntil} from 'rxjs/operators';
import {Glider, GliderStatus} from '@app/core/interfaces/glider.interace';
import {FormControl} from '@angular/forms';
import {typeAction} from '@app/shared/utils/enum';
import {
  GetSortOrder,
  GetSortOrderObject,
  ReplaceElementArray,
} from '@app/shared/utils/utils';
import {statusFleets} from '@app/fleet/enum/enums';
import {SharedVarService} from '@app/services/SharedVarService/shared-var-service.service';
import {environment} from '@src/environments/environment';

@Component({
  selector: 'app-fleet-list',
  templateUrl: './fleet-list.component.html',
  styleUrls: ['./fleet-list.component.css'],
  encapsulation: ViewEncapsulation.None,
})
export class FleetListComponent implements OnInit, OnDestroy, AfterViewInit {
  displayedColumns: string[] = [
    'name',
    'ip',
    'status',
    'region',
    'manufacturing_date',
    'pixhawk',
    'company'
  ];
  dataSource = new MatTableDataSource<Fleet>();
  subject = new Subject<any>();
  data: Fleet[] = [];
  filterStatus: GliderStatus[] = [];
  status = statusFleets;
  urlNewbornGliderWard = environment.urlNewbornGliderWard;

  statusFilter = new FormControl('');
  nameFilter = new FormControl('');

  filterValues = {
    status: '',
    name: '',
  };

  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @Output() eventData = new EventEmitter<boolean>();

  constructor(
    private fleetService: FleetService,
    public dialog: MatDialog,
    private notificationsAlerts: NotificationsService,
    private sharedVarService: SharedVarService
  ) {
  }

  ngOnInit(): void {
    this.sharedVarService.setValue('Fleets');
    this.loadData();

    this.fleetService
      .listenDataSubjectFleet()
      .pipe(
        takeUntil(this.subject),
        catchError((err) => {
          this.notificationsAlerts.openSnackBar('Error', 'Ok');
          return of(err);
        })
      )
      .subscribe((res) => {
        if (res) {
          if (this.fleetService.action === typeAction.updated) {
            this.data = ReplaceElementArray(this.data, res);
          } else {
            this.data.push(res);
          }
          this.data = this.sortByInUse(this.data).filter(fleet => fleet.inUse);
          this.dataSource.data = [...this.data];
        }
      });

    // Filter

    this.fleetService
      .getFleetStatusList()
      .pipe(
        takeUntil(this.subject),
        catchError((err) => {
          this.notificationsAlerts.openSnackBar('Error', 'Ok');
          return of(err);
        })
      )
      .subscribe((res) => {
        this.filterStatus = res;
      });

    this.fieldListener();
  }

  ngAfterViewInit(): void {
    this.dataSource.filterPredicate = this.createFilter();
    this.dataSource.paginator = this.paginator;
  }

  loadData(): void {
    this.fleetService
      .getFleetList()
      .pipe(
        takeUntil(this.subject),
        catchError((err) => {
          this.notificationsAlerts.openSnackBar('Error', 'Ok');
          return of(err);
        })
      )
      .subscribe((res) => {
        this.data = this.sortByInUse(res).filter(fleet => fleet.inUse);
        this.dataSource.data = this.data;
        this.data.length
          ? this.eventData.emit(false)
          : this.eventData.emit(false);
      });
  }

  private createFilter(): (fleet: Fleet, filter: string) => boolean {
    let filterFunction = function(fleet: any, filter: any): boolean {
      let searchTerms = JSON.parse(filter);

      let qualifies: boolean = true;

      if (searchTerms.status) {
        qualifies =
          fleet.status &&
          fleet.status.name &&
          fleet.status.name.indexOf(searchTerms.status) !== -1;
      }

      if (searchTerms.name) {
        qualifies =
          qualifies &&
          fleet.name &&
          fleet.name.toLowerCase().indexOf(searchTerms.name.toLowerCase()) !==
          -1;
      }

      return qualifies;
    };

    return filterFunction;
  }

  private sortByInUse(fleets: Fleet[]): Fleet[] {
    return [...fleets].sort((a, b) => {
      if (a.inUse === b.inUse) {
        return 0;
      }
      return a.inUse ? -1 : 1;
    });
  }

  private fieldListener() {
    this.nameFilter.valueChanges.subscribe((name) => {
      this.filterValues.name = name;
      this.dataSource.filter = JSON.stringify(this.filterValues);
    });

    this.statusFilter.valueChanges.subscribe((status) => {
      this.filterValues.status = status;
      this.dataSource.filter = JSON.stringify(this.filterValues);
    });
  }

  clearfilters(): void {
    this.nameFilter.setValue('');
    this.statusFilter.setValue('');
    this.filterValues.name = '';
    this.filterValues.status = '';
  }

  orderBy(column: string): void {
    this.data = this.dataSource.data = this.data.sort(GetSortOrder(column));
  }

  orderByObject(keyObject: string, column: string): void {
    this.data = this.dataSource.data = this.data.sort(
      GetSortOrderObject(keyObject, column)
    );
  }

  ngOnDestroy(): void {
    this.subject.complete();
    this.subject.unsubscribe();
  }
}
