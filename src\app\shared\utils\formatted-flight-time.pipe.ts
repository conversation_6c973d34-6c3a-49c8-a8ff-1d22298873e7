import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'formattedFlightTime'
})
export class FormattedFlightTimePipe implements PipeTransform {
  transform(seconds: number | null | undefined): string {
    if (!seconds || seconds <= 0) {
      return 'No data';
    }

    const totalMinutes = Math.floor(seconds / 60);
    const totalHours = Math.floor(totalMinutes / 60);
    const totalDays = Math.floor(totalHours / 24);

    const secondsRemainder = Math.floor(seconds % 60);
    const minutesRemainder = Math.floor(totalMinutes % 60);
    const hoursRemainder = Math.floor(totalHours % 24);

    let result = '';

    if (totalDays > 0) {
      result += `${totalDays}d `;
    }

    result += `${hoursRemainder}h ${minutesRemainder}m ${secondsRemainder}s`;

    return result.trim();
  }
}
